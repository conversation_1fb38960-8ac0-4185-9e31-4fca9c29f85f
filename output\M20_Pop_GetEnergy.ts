import $2ListenID from "./ListenID";
import $2VideoButton from "./VideoButton";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_Pop_GetEnergy")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup)
export default class M20_Pop_GetEnergy extends $2Pop.Pop {
    @property(cc.Label)
    coincost: cc.Label = null;

    @property(cc.Label)
    coinget: cc.Label = null;

    @property(cc.Label)
    coincount: cc.Label = null;

    @property(cc.Label)
    adcost: cc.Label = null;

    @property(cc.Label)
    adget: cc.Label = null;

    @property(cc.Label)
    adcount: cc.Label = null;

    @property(cc.Node)
    adgray: cc.Node = null;

    @property(cc.Button)
    costgray: cc.Button = null;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    changeListener(t: any) {
        super.changeListener(t);
    }

    onOpen() {
        this.mode.dailyAdpack.has("energycoin") || this.mode.dailyAdpack.addGoods("energycoin", $2Manager.Manager.vo.switchVo.diamondBuyStamina[2]);
            this.mode.dailyAdpack.has("energyad") || this.mode.dailyAdpack.addGoods("energyad", $2Manager.Manager.vo.switchVo.adBuyStamina[2]);
            this.data = {
              coin: {
                cost: $2Manager.Manager.vo.switchVo.diamondBuyStamina[0],
                get: $2Manager.Manager.vo.switchVo.diamondBuyStamina[1],
                count: this.mode.dailyAdpack.getVal("energycoin")
              },
              ad: {
                cost: $2Manager.Manager.vo.switchVo.adBuyStamina[0],
                get: $2Manager.Manager.vo.switchVo.adBuyStamina[1],
                count: this.mode.dailyAdpack.getVal("energyad")
              }
            };
            this.adgray.getChildByName("gray").active = this.data.ad.count <= 0;
            this.costgray.node.getChildByName("gray").active = this.data.coin.count <= 0;
            this.adgray.getComponent($2VideoButton.default).interactable = this.data.ad.count > 0;
            this.costgray.interactable = this.data.coin.count > 0;
            this.coincost.string = this.data.coin.cost;
            this.coincount.string = cc.js.formatStr("今日剩余%d次", this.data.coin.count);
            this.coinget.string = this.data.coin.get;
            this.adcount.string = cc.js.formatStr("今日剩余%d次", this.data.ad.count);
            this.adget.string = this.data.ad.get;
    }

    costGet() {
        if (this.mode.currencyIsEnough($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond, this.data.coin.cost)) {
              this.mode.dailyAdpack.useUp("energycoin");
              $2Manager.Manager.vo.knapsackVo.useUp($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond, this.data.coin.cost);
              $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, this.data.coin.get);
              $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("体力+%d", this.data.coin.get));
              $2Notifier.Notifier.send($2ListenID.ListenID.Item_GoodsChange);
              this.close();
            } else {
              $2AlertManager.AlertManager.showNormalTips("灵币不足~");
            }
    }

    adGet() {
        cc.log(this.data.ad);
            this.mode.dailyAdpack.useUp("energyad");
            $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, this.data.ad.get);
            $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("体力+%d", this.data.ad.get));
            $2Notifier.Notifier.send($2ListenID.ListenID.Item_GoodsChange);
            this.close();
    }

}
