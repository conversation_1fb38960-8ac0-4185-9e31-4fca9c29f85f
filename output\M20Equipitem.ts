import $2L<PERSON>enID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20Equipitem extends cc.Component {
    @property(cc.Label)
    equipName: cc.Label = null;

    @property(cc.Sprite)
    equipImg: cc.Sprite = null;

    @property(cc.Sprite)
    equipBg: cc.Sprite = null;

    @property(cc.Sprite)
    equipCellIcon: cc.Sprite = null;

    @property(cc.Label)
    equipFrame: cc.Label = null;

    @property(cc.Label)
    equipLv: cc.Label = null;

    @property(cc.Node)
    upgradeNode: cc.Node = null;

    @property(cc.Node)
    puzzleNode: cc.Node = null;

    constructor() {
        super();
        this.eueiplvcfgs = [];
    }

    get equipID() {
        return this.equipcfg.id;
    }

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get view() {
        return $2UIManager.UIManager.getView("ui/ModeBackpackHero/M20_PrePare_Equip");
    }

    setInfo(e: any) {
        const t = this.equipcfg = $2Cfg.Cfg.RoleUnlock.find({
              id: e
            });
            this.eueiplvcfgs.length = 0;
            if (3 == t.type) {
              this.eueiplvcfgs = [...$2Cfg.Cfg.RoleLv.filter({
                roleId: e
              }]);
            } else {
              this.eueiplvcfgs = [...$2Cfg.Cfg.EquipLv.filter({
                equipId: e
              }]);
            }
            if (3 != this.equipcfg.type) {
              // $2Manager.Manager.loader.loadSpriteToSprit("img/ModeBackpackHero/bg_option_0" + this.equipcfg.rarity, this.equipBg, this.view.node);
              $2Manager.Manager.loader.loadSpriteToSprit("v1/images/bg/bg_option_0" + this.equipcfg.rarity, this.equipBg, this.view.node);
              $2Manager.Manager.loader.loadSpriteToSprit(this.equipcfg.icon, this.equipImg, this.view.node);
            }
            this.equipName.string = this.equipcfg.roleName;
            this.resetState();
    }

    resetState() {
        const e = 3 == this.equipcfg.type || this.mode.userEquipPack.has(this.equipID);
            this.equipFrame.node.parent.setActive(e);
            const t = 3 == this.equipcfg.type ? Math.max(this.mode.fightinfopack.getVal("role" + this.equipcfg.id), 1) : this.mode.getEquipLv(this.equipcfg.id);
            this.equipLv.string = t >= this.eueiplvcfgs[this.eueiplvcfgs.length - 1].lv ? "已满级" : cc.js.formatStr("等级%d", t);
            if (e) {
              const o = this.mode.fragmentsPack.getVal(this.equipcfg.id);
              const i = this.eueiplvcfgs.find(function (e) {
                return e.lv == t;
              }).upgradeNeedle;
              this.equipFrame.string = o + "/" + i;
              this.getComponentInChildren(cc.ProgressBar).progress = o / i;
              this.upgradeNode.setActive(o >= i);
              if (this.upgradeNode.active) {
                cc.tween(this.upgradeNode).to(.5, {
                  y: 0
                }).to(.5, {
                  y: 10
                }).union().repeatForever().start();
              } else {
                cc.Tween.stopAllByTarget(this.upgradeNode);
                this.upgradeNode.setPosition(0, -5);
              }
              this.puzzleNode.setActive(!(o >= i));
            }
    }

    showInfo() {
        $2Notifier.Notifier.send($2ListenID.ListenID.M20_ShowEquipInfo, $2MVC.MVC.openArgs().setParam({
              equipid: this.equipcfg.id
            }));
    }

    setClickCall(e: any) {
        this._onClickCall = e;
    }

    onClick() {
        this._onClickCall && this._onClickCall(this);
    }

}
