import $2AutoAmTool from "./AutoAmTool";
import $2Cfg from "./Cfg";
import $2SoundCfg from "./SoundCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2FightUIView from "./FightUIView";
import $2Game from "./Game";
import $2MChains from "./MChains";
import $2ModeChainsModel from "./ModeChainsModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeChains/M33_FightUIView")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Main)
export default class M33_FightUIView extends $2FightUIView.FightUIView {
    @property(cc.Node)
    packLayout: cc.Node = null;

    @property(cc.Node)
    finger: cc.Node = null;

    get mode() {
        return $2ModeChainsModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    changeListener(t: any) {
        super.changeListener(t);
            this.changeToch(t);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Item_GoodsChange, this.onItem_GoodsChange, this);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_GameRoundType, this.onFight_RoundState, this);
    }

    changeToch(e: any) {
        this.node.changeListener(e, cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
            this.node.changeListener(e, cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
            this.node.changeListener(e, cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    setInfo() {
        const e = this;
            $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ShowGameTips, 3, "巨龙来袭");
            this.game.canSkill = false;
            $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.sfx_dragonappear);
            this.scheduleOnce(function () {
              e.game.canSkill = true;
            }, 2);
            if (this.game.isChallenge) {
              this.node.getComByPath(cc.Label, "bg/topUI/level").string = this.game.miniGameCfg.name;
            } else {
              this.node.getComByPath(cc.Label, "bg/topUI/level").string = cc.js.formatStr("当前第%d关", this.game.miniGameCfg.lvid % 1e3);
            }
            const t = $2GameSeting.GameSeting.getDiffDef(this.game.miniGameCfg.type);
            this.node.getComByPath(cc.RichText, "bg/topUI/diff").setAttribute({
              // string: "<b>" + cc.js.formatStr("<outline color=black width=3>难度:<color=%s>%s</c>", t.colorStr, t.name)
              string: cc.js.formatStr("<outline color=black width=3>难度:<color=%s>%s</c>", t.colorStr, t.name)
            }).node.setActive(this.game.passType != $2MChains.MChains.PassType.Move && $2Manager.Manager.vo.switchVo.diffSelect);
            this.BossLifeBar = this.node.getComByPath(cc.ProgressBar, "bg/topUI/BossLifeBar/progressBar");
            this.BossLifeBar.node.parent.setActive(false);
            if (1 == $2Manager.Manager.vo.userVo.guideIndex) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
              cc.tween(this.finger).to(1, {
                x: 150
              }, {
                easing: cc.easing.sineInOut
              }).to(1, {
                x: -150
              }, {
                easing: cc.easing.sineInOut
              }).union().repeatForever().start();
            } else {
              this.finger.parent.setActive(false);
            }
    }

    onClickDamagePanel(e: any) {
        const t = e.target;
            if (1 == t.childrenCount) {
              t.children[0].setActive(!t.children[0].active);
            } else {
              if (99 == t.t_tempID) {
                return;
              }
              t.t_tempID = 99;
              $2Manager.Manager.loader.loadPrefab("ui/fight/DamagePanel", this.game.gameNode).then(function (e) {
                e.setAttribute({
                  parent: t,
                  position: cc.Vec2.ZERO
                });
              });
            }
    }

    onItem_GoodsChange(e: any, t: any) {
        const o = this;
            const i = $2Cfg.Cfg.CurrencyConfig.get(t);
            if ((null == i ? undefined : i.type) == $2GameSeting.GameSeting.GoodsType.DragonBall) {
              $2UIManager.UIManager.OpenInQueue("ui/ModeChains/M33_FightBuffView", $2MVC.MVC.openArgs().setParam({
                type: $2MChains.MChains.poolType.DragonBall,
                getPool: function (e) {
                  return o.mode.fightBuffWidth($2MChains.MChains.poolType.DragonBall, e);
                }
              }).setDailyTime(.3));
              this.packLayout.hideAllChildren();
              this.game.knapsackMgr.filter(function (e) {
                return e.type == $2GameSeting.GameSeting.GoodsType.DragonBall;
              }).forEach(function (e, t) {
                i = $2Cfg.Cfg.CurrencyConfig.get(e.id);
                const n = o.packLayout.children[t] || cc.instantiate(o.packLayout.children[0]);
                n.setAttribute({
                  parent: o.packLayout,
                  active: true,
                  scale: .5
                });
                $2Manager.Manager.loader.loadSpriteToSprit(i.icon, n.getComponent(cc.Sprite), o.node);
              });
            }
    }

    onFight_RoundState() {
        const e = this;
            if (this.game.bronMonsterMgr.cutStatus == $2MChains.MChains.RoundStatus.TRANSITIONAM) {
              this.packLayout.children.forReverse(function (t, o) {
                t.setAttribute({
                  parent: e.node,
                  active: true,
                  position: t.wordPos
                });
                const i = $2GameUtil.GameUtil.AngleAndLenToPos(51 * o, 200).clone();
                cc.tween(t).delay(.1 * o).to(.5, {
                  position: i,
                  scale: .7
                }).by(.5, {
                  y: 20
                }).by(.5, {
                  y: -20
                }).by(.8, {
                  y: 1500
                }).start();
              });
              this.scheduleOnce(function () {
                e.newCloudAm();
              }, 2);
              this.scheduleOnce(function () {
                $2Manager.Manager.loader.loadSpineNode("bones/ui/cloud", {
                  nodeAttr: {
                    parent: e.node,
                    position: cc.Vec2.ZERO,
                    scale: 1,
                    group: "UI"
                  },
                  spAttr: {
                    animation: "animation",
                    loop: false,
                    type: $2GameSeting.GameSeting.TweenType.Not
                  },
                  delayRemove: 3
                });
              }, 1.5);
              this.scheduleOnce(function () {
                e.game.bronMonsterMgr.changeGameStatus($2MChains.MChains.RoundStatus.BOSSCOMEON);
              }, 3);
            }
    }

    newCloudAm() {
        const e = this.game.scenceSize;
            const t = [];
            $2Manager.Manager.loader.loadSpriteImg("v1/images/fight/yun01", {
              nodeAttr: {
                parent: this.game.botEffectNode,
                position: cc.v2(e[0], e[3] - 200 + $2GameUtil.GameUtil.random(-30, 30)),
                scale: 2,
                opacity: 0
              }
            }).then(function (e) {
              return t.push(e);
            });
            $2Manager.Manager.loader.loadSpriteImg("v1/images/fight/yun01", {
              nodeAttr: {
                parent: this.game.botEffectNode,
                position: cc.v2(e[1], e[3] - 200 + $2GameUtil.GameUtil.random(-30, 30)),
                scale: 2,
                opacity: 0
              }
            }).then(function (e) {
              return t.push(e);
            });
            $2Manager.Manager.loader.loadSpriteImg("v1/images/fight/yun02", {
              nodeAttr: {
                parent: this.game._topEffectNode,
                position: cc.v2(e[0], e[3] + 100 + $2GameUtil.GameUtil.random(-30, 30)),
                scale: 1.5,
                opacity: 0
              }
            }).then(function (e) {
              return t.push(e);
            });
            $2Manager.Manager.loader.loadSpriteImg("v1/images/fight/yun02", {
              nodeAttr: {
                parent: this.game._topEffectNode,
                position: cc.v2(e[1], e[3] + 100 + $2GameUtil.GameUtil.random(-30, 30)),
                scale: 1.5,
                opacity: 0
              }
            }).then(function (e) {
              return t.push(e);
            });
            this.scheduleOnce(function () {
              t.forEach(function (e) {
                const t = e.addComponent($2AutoAmTool.default);
                t.amTime = $2GameUtil.GameUtil.random(30, 40) / 10;
                t.resetType(3);
                cc.tween(e).to(1, {
                  opacity: 255
                }).start();
              });
            }, 1);
    }

    onTouchMove(e: any) {
        if (this.game.gameState == $2Game.Game.State.START) {
              if (this.game.passType == $2MChains.MChains.PassType.D360) {
                const t = $2Game.Game.touchToGamePos(e.getLocation());
                const o = $2GameUtil.GameUtil.GetAngle(this.role.position, t) + 90;
                this.game.lineNode.opacity = 155;
                this.game.lineNode.angle = o;
                this.role.forwardDirection.set($2GameUtil.GameUtil.AngleAndLenToPos(o));
                this.role.roleNode.angle = o + 180;
              } else if (this.game.passType == $2MChains.MChains.PassType.Move) {
                S.set(e.getLocation().sub(e.getPreviousLocation())).divSelf(this.game.gameCamera.cutZoomRatio);
                S.addSelf(this.role.position);
                this.role.setPosition(S);
              } else {
                S.set(e.getLocation().sub(e.getPreviousLocation())).divSelf(this.game.gameCamera.cutZoomRatio);
                this.role.node.x = cc.misc.clampf(S.x + this.role.position.x, this.game.scenceSize[0] + 50, this.game.scenceSize[1] - 50);
              }
            }
    }

    onTouchEnd() {
        $2Game.Game.tween(this.game.lineNode).to(.2, {
              opacity: 0
            }).start();
            this.finger.parent.setActive(false);
    }

    onUpdate() {
        this.game.bronMonsterMgr.cutStatus == $2MChains.MChains.RoundStatus.BATTLE && this.BossLifeBar.node.parent.active && (this.BossLifeBar.progress = cc.misc.clamp01(this.game.bossHp / this.game.bossMaxHp));
    }

    onShowFinish() {
        this.offTouch();
    }

}
