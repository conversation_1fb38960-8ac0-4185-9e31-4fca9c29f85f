import $2Call<PERSON> from "./CallID";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20EquipitemBlock from "./M20EquipitemBlock";
import $2M20EquipitemList from "./M20EquipitemList";

const { ccclass, property, menu } = cc._decorator;

enum s {
    Start = 1,
    Equip = 2
}

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_PrePare_Equip")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel)
export default class M20_PrePare_Equip extends $2Pop.Pop {
    @property(cc.Node)
    equipBox: cc.Node = null;

    @property(cc.Node)
    unlockBox: cc.Node = null;

    @property(cc.Node)
    notUnlockBox: cc.Node = null;

    @property(cc.Prefab)
    equipGridItem: cc.Prefab = null;

    @property(cc.Prefab)
    equiplockitem: cc.Prefab = null;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get equippack() {
        return this.mode.userEquipPack.filter(function (e) {
        return e.isFitOut;
    }

    changeListener(e: any) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.resetAll, this, -200);
            $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Refresh_List, this.resetAll, this);
            $2Notifier.Notifier.changeCall(e, $2CallID.CallID.M20_SelectEquip, this.getThis, this);
    }

    getThis() {
        return this;
    }

    setInfo() {
        this.stateType = s.Start;
            const e = cc.find("bg/usingnode/bgBox", this.node);
            for (const t = 0; t < 8; t++) {
              e.children[t] || cc.instantiate(e.children[0]).setAttribute({
                parent: e
              });
            }
            this.resetAll();
    }

    resetAll() {
        const e = this;
            this.resetEquip();
            this.resetUnlock();
            this.resetNotUnlock();
            if (11 == $2Manager.Manager.vo.userVo.guideIndex) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
              this.scheduleOnce(function () {
                $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
                  targetNode: e.equipBox.children[0]
                });
              }, 0);
            }
    }

    onClickItem(e: any) {
        if (this.stateType == s.Equip && e.isEquip) {
              this.mode.userEquipPack.replace(e.equipID, this.cutSelectEquip.equipID);
              this.stateType = s.Start;
              $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_List);
            } else if (this.stateType == s.Start && ([...this.equipBox.children, this.unlockBox.children].forEach(function (t) {
              const o = t.getComponent($2M20EquipitemList.default);
              o != e && o.resetState();
            }), 12 == $2Manager.Manager.vo.userVo.guideIndex || 16 == $2Manager.Manager.vo.userVo.guideIndex)) {
              const t = 12 == $2Manager.Manager.vo.userVo.guideIndex ? e.dropNode.children[0] : e.btnuse;
              $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
                targetNode: t,
                offset: cc.v2(0, 10)
              });
            }
    }

    onSelectItem(e: any) {
        16 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
            if (this.equippack.length < 8) {
              this.mode.AssembleEquip(e.equipID);
              $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_List);
            } else {
              this.cutSelectEquip = e;
              this.stateType = s.Equip;
              $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_Item, e);
            }
    }

    resetEquip() {
        const e = this;
            this.equipBox.hideAllChildren();
            const t = this.equipBox.getComponent(cc.Layout);
            t.enabled = true;
            this.equippack.forEach(function (t, o) {
              const i = e.equipBox.children[o] || cc.instantiate(e.equipGridItem);
              i.setAttribute({
                parent: e.equipBox,
                active: true,
                zIndex: o
              });
              const n = i.getComponent($2M20EquipitemList.default);
              n.setInfo(+t.id);
              n.setClickCall(e.onClickItem.bind(e));
            });
            this.scheduleOnce(function () {
              t && (t.enabled = false);
            });
    }

    resetUnlock() {
        const e = this;
            const t = this.unlockBox.getComponent(cc.Layout);
            t.enabled = true;
            this.unlockBox.hideAllChildren();
            const o = this.mode.userEquipPack.filter(function (e) {
              return !e.isFitOut;
            }).map(function (e) {
              return $2Cfg.Cfg.RoleUnlock.get(e.id);
            });
            o.sort(function (e, t) {
              return t.Count - e.Count;
            });
            o.forEach(function (t, o) {
              const i = e.unlockBox.children[o] || cc.instantiate(e.equipGridItem);
              i.setAttribute({
                parent: e.unlockBox,
                active: true,
                zIndex: o
              });
              const n = i.getComponent($2M20EquipitemList.default);
              n.setInfo(+t.id);
              n.setClickCall(e.onClickItem.bind(e));
            });
            this.scheduleOnce(function () {
              t.enabled = false;
            });
    }

    resetNotUnlock() {
        const e = this;
            this.notUnlockBox.hideAllChildren();
            const t = $2Cfg.Cfg.RoleUnlock.getArray().filter(function (t) {
              return 1 == t.type && t.unlock && !e.mode.checkIsUnlock(t);
            });
            t.sort(function (e, t) {
              if (e.rarity == $2GameSeting.GameSeting.RarityType.S || t.rarity == $2GameSeting.GameSeting.RarityType.S) {
                return -1;
              } else {
                return e.Count - t.Count;
              }
            });
            t.forEach(function (t, o) {
              const i = e.notUnlockBox.children[o] || cc.instantiate(e.equiplockitem);
              i.setAttribute({
                parent: e.notUnlockBox,
                active: true
              });
              i.getComponent($2M20EquipitemBlock.default).setInfo(t.id);
            });
    }

    onShowFinish() {
        let e: any;
            let t: any;
            null === (t = null === (e = this.param) || e === undefined ? undefined : e.showCb) || t === undefined || t.call(e, this.node);
            this.node.opacity = 255;
    }

    onOpen() {
        this.node.opacity = 0;
    }

}
