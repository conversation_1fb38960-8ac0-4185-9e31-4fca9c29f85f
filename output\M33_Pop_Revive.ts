import $2ListenID from "./ListenID";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeChainsModel from "./ModeChainsModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeChains/M33_Pop_Revive")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel)
export default class M33_Pop_Revive extends $2Pop.Pop {
    get mode() {
        return $2ModeChainsModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    setInfo() {
        this.game.clearAllBullet();
            this.labelArr[0].string = "(" + (this.game.mainRole.reliveNum + 1) + "/" + $2Manager.Manager.vo.switchVo.dragonRevive + ")";
    }

    onClickRelive() {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_ReliveSuccess);
            this.close();
    }

    onClickGiveUp() {
        $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End);
            this.close();
    }

    onClickFrame() {
        this.onClickGiveUp();
    }

}
