import $2CallID from "./$2CallID";
import $2GameSeting from "./$2GameSeting";
import $2ListenID from "./$2ListenID";
import $2Cfg from "./$2Cfg";
import $2MVC from "./$2MVC";
import $2Pop from "./$2Pop";
import $2Notifier from "./$2Notifier";
import $2Manager from "./$2Manager";
import $2UIManager from "./$2UIManager";
import $2RBadgeModel from "./$2RBadgeModel";
import $2Game from "./$2Game";
import $2ItemModel from "./$2ItemModel";
import $2ModeBackpackHeroModel from "./$2ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_PrePare_Fight")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.SubPopup)
export default class M20_PrePare_Fight extends $2Pop.Pop {
    @property(cc.Node)
    nodeItem: cc.Node = null;

    @property(cc.Label)
    unlockinfo: cc.Label = null;

    @property(cc.Prefab)
    rewardPre: cc.Prefab = null;

    @property(cc.Button)
    btnSweeping: cc.Button = null;

    @property(cc.Node)
    btnSweepingAd: cc.Node = null;

    @property(cc.Node)
    boxnode: cc.Node = null;

    @property(cc.Node)
    pagelbtn: cc.Node = null;

    @property(cc.Node)
    pagerbtn: cc.Node = null;

    @property(cc.Node)
    leftnode: cc.Node = null;

    @property(cc.Node)
    rightnode: cc.Node = null;

    @property(cc.Node)
    rewardRedpoint: cc.Node = null;

    @property(cc.Node)
    signinRedpoint: cc.Node = null;

    @property(cc.Node)
    DiffSelect: cc.Node = null;

    @property(cc.Node)
    talk: cc.Node = null;

    lvcfg: any = null;
    isontween: boolean = false;
    redpointinit: boolean = false;
    curSelectPass: any;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    get signdata() {
        return this.mode.rVo;
    }

    get boxtree() {
        return this.mode.treemap && this.mode.treemap.fight;
    }

    changeListener(isAdd: boolean) {
        super.changeListener(isAdd);
        Notifier.Notifier.changeListener(isAdd, ListenID.ListenID.ResetView, this.setInfo, this);
        Notifier.Notifier.changeListener(isAdd, ListenID.ListenID.Main_ResetView, this.resetview, this);
        Notifier.Notifier.changeListener(isAdd, ListenID.ListenID.ModeBack_RoundUnlock, this.onRoundUnlock, this);
        Notifier.Notifier.changeListener(isAdd, ListenID.ListenID.Shop_InfoUpDate, this.resetview, this);
        Notifier.Notifier.changeListener(isAdd, ListenID.ListenID.FightHightRound, this.onFightHightRound, this);
        Notifier.Notifier.changeListener(isAdd, ListenID.ListenID.Fight_PrePare_Refresh_SweepView, this.setSweepView, this);
        Notifier.Notifier.changeListener(isAdd, ListenID.ListenID.PrePare_Fight_Skip, this.loadItem, this);
    }

    pageChange(event: any, customEventData: string, duration: number = 0.2) {
        const offset = +customEventData;
        const targetLvid = this.curSelectPass.lvid + offset;
        const mainID = Manager.Manager.leveMgr.getPassMainID(targetLvid);
        this.loadItem(mainID);
    }

    onFightHightRound() {
        this.loadItem(this.curSelectPass.lvid);
    }

    onRoundUnlock() {
        if (Manager.Manager.vo.userVo.guideIndex >= 17) {
            this.nodeArr[1].getChildByName("hand").setActive(true);
        }
    }

    onBtn(event: any, customEventData: string) {
        const menuView = Notifier.Notifier.call(CallID.CallID.M20_GetMenuView);
        Manager.Manager.leveMgr.PlayingLv = this.curSelectPass.lvid;
        menuView.onBtn(event, customEventData, () => {
            UIManager.UIManager.Open("ui/ModeChains/M33_Pop_GameEnd", MVC.MVC.openArgs().setParam({
                isWin: true,
                cfg: this.curSelectPass
            }).setIsNeedLoading(false));
            this.loadItem(this.curSelectPass.lvid);
            Notifier.Notifier.send(ListenID.ListenID.Task_UpdateProgress, 2);
        });
        if (customEventData.includes("mode")) {
            this.nodeArr[1].getChildByName("hand").setActive(false);
        }
    }

    loadItem(lvid: number) {
        const maxLvid = Cfg.Cfg.BagModeLv.getArray().filter(e => e.type === 1).lastVal.lvid;
        const challengeLv = cc.misc.clampf(Manager.Manager.leveMgr.CurChallengeLv + 2, 5, maxLvid);
        const mainID = Manager.Manager.leveMgr.getPassMainID(lvid);
        
        this.talk.setAttribute({
            active: false,
            scale: 0
        });

        const cfg = this.curSelectPass = Cfg.Cfg.BagModeLv.get(lvid);
        const isNormalMode = cfg.type === 1;
        const maxRound = Manager.Manager.leveMgr.getLvMaxRound(lvid);

        if (isNormalMode) {
            this.setSweepView();
        } else {
            this.btnSweeping.node.setActive(false);
        }

        const diffDef = GameSeting.GameSeting.getDiffDef(cfg.type);
        this.DiffSelect.getComByChild(cc.RichText).text = cc.js.formatStr(
            "<outline color=black width=3>难度:<color=%s>%s</c>", 
            diffDef.colorStr, 
            diffDef.name
        );

        if (cfg.lvicon) {
            Manager.Manager.loader.loadSpriteToSprit(cfg.lvicon, this.nodeItem.getComponentInChildren(cc.Sprite), this.node);
        }

        this.nodeItem.getComByChild(cc.Label, "title").setAttribute({
            string: cc.js.formatStr("%s.%s", cfg.lvid % 1000, cfg.name)
        }).node.setAttribute({
            color: cc.Color.WHITE
        });

        Manager.Manager.loader.loadSpriteToSprit(
            "img/ModeChains/bg/" + ["bg_ptms", "bg_knms", "bg_dyms"][cfg.type - 1], 
            this.node.getComByPath(cc.Sprite, "maskbg"), 
            this.node
        );

        cc.tween(this.nodeItem)
            .stopLast()
            .to(0.1, { scale: 1.01 })
            .to(0.1, { scale: 1 })
            .start();

        // 处理奖励箱子
        cfg.waveReward.forEach((reward: any, index: number) => {
            const boxNode = this.boxnode.children[index] || cc.instantiate(this.boxnode.children[0]).setAttribute({
                parent: this.boxnode
            });
            
            const rewardData = Manager.Manager.leveMgr.getLvRewardData(lvid, index);
            const canGet = cfg.wave[index] <= maxRound && !rewardData;
            
            boxNode.getComByChild(cc.Label, "wave").string = canGet ? "可领取" : cc.js.formatStr("%d%", cfg.wave[index]);
            Manager.Manager.loader.loadSpriteToSprit(
                "img/ModeBackpackHero/boxicon" + ["", "2", "3"][index] + "_0" + (rewardData ? "2" : "1"), 
                boxNode.getComByChild(cc.Sprite, "icon")
            );
            
            boxNode.getChildByName("redpoint").setActive(canGet);
            boxNode.targetOff(this);
            cc.Tween.stopAllByTarget(boxNode);
            
            if (canGet) {
                cc.tween(boxNode)
                    .sequence(
                        cc.tween().to(0.3, { scale: 1 }, { easing: "sineInOut" }),
                        cc.tween().to(0.3, { scale: 1.1 }, { easing: "sineInOut" })
                    )
                    .repeatForever()
                    .start();
            }
            
            boxNode.on(cc.Node.EventType.TOUCH_END, () => {
                if (canGet) {
                    this.getRewardItem(cfg, index);
                } else {
                    this.showRewardItem(cfg, index, boxNode.wordPos);
                }
            }, this);
        });

        const isUnlocked = Manager.Manager.leveMgr.vo.lvIdUnlock.includes(lvid);
        
        if (isNormalMode) {
            if (!isUnlocked) {
                this.unlockinfo.string = cc.js.formatStr("通关第%d章后解锁", lvid - 1);
            }
        } else if (cfg.type === GameSeting.GameSeting.DiffType.Hard && Manager.Manager.leveMgr.vo.curPassLv < 6) {
            this.unlockinfo.string = cc.js.formatStr("通关第%d章后解锁", 6);
        } else if (cfg.type === GameSeting.GameSeting.DiffType.Hell && Manager.Manager.leveMgr.vo.curPassLv < 8) {
            this.unlockinfo.string = cc.js.formatStr("通关第%d章后解锁", 8);
        } else if (!isUnlocked) {
            this.unlockinfo.string = cc.js.formatStr("通关%s关卡后解锁", GameSeting.GameSeting.getDiffDef(cfg.type - 1).name);
        }

        this.btnSweepingAd.parent.setActive(isUnlocked);
        this.nodeArr[1].active = isUnlocked;
        this.nodeItem.getChildByName("lock").active = !isUnlocked;

        const isPassed = Manager.Manager.leveMgr.checkLvIsPass(lvid);
        const unlockLabel = this.nodeItem.getChildByName("unlockinfo").getComponent(cc.Label);
        const statusText = maxRound === 0 ? "未通关" : cc.js.formatStr("历史最佳") + " " + maxRound + "%";
        unlockLabel.string = isPassed ? "已通关" : statusText;

        this.labelArr[0].string = "x" + Manager.Manager.vo.switchVo.fightStamina;

        if (isNormalMode) {
            this.pagelbtn.active = lvid > 1;
            this.pagerbtn.active = lvid < challengeLv;
        }

        cc.tween(this.nodeArr[1])
            .stopLast()
            .set({ scale: 1 })
            .start();

        if (!isPassed) {
            cc.tween(this.nodeArr[1])
                .sequence(
                    cc.tween().to(0.7, { scale: 1.05 }, { easing: "sineInOut" }),
                    cc.tween().to(0.7, { scale: 1 }, { easing: "sineInOut" })
                )
                .repeatForever()
                .start();
        }

        this.nodeItem.getChildByName("passImg").opacity = isUnlocked ? 255 : 100;
        this.unlockinfo.node.active = !isUnlocked;

        const rightRewards = this.checkCanReward(Cfg.Cfg.BagModeLv.find({ lvid: mainID + 1 }));
        this.rightnode.setActive(rightRewards.length > 0);

        const leftRewards = this.checkCanReward(Cfg.Cfg.BagModeLv.find({ lvid: mainID - 1 }));
        this.leftnode.setActive(leftRewards.length > 0);

        if (Manager.Manager.leveMgr.vo.curPassLv <= 5 &&
            Manager.Manager.vo.userVo.guideIndex >= 17) {
            this.nodeArr[1].getChildByName("hand").setActive(cfg.lvid === Manager.Manager.leveMgr.CurChallengeLv);
        }
    }

    selectDiff(diffType: number) {
        const currentLv = this.curSelectPass.lvid % 1000;
        this.loadItem(1000 * (diffType - 1) + currentLv);
    }

    setSweepView() {
        const sweepCount = Manager.Manager.leveMgr.vo.dailyData.sweep_count;
        const sweepCountAd = Manager.Manager.leveMgr.vo.dailyData.sweep_count_ad;
        const isPassed = Manager.Manager.leveMgr.checkLvIsPass(this.curSelectPass.lvid);
        const isNormalMode = this.curSelectPass.type === 1;

        if (!isPassed || !isNormalMode) {
            this.btnSweeping.node.setActive(false);
            this.btnSweepingAd.setActive(false);
            return;
        }

        console.log(Manager.Manager.vo.switchVo.lvSweep);
        const canNormalSweep = sweepCount > 0;
        console.log(Manager.Manager.leveMgr.vo.dailyData.isADSweepNum);
        const canAdSweep = sweepCount <= 0 && sweepCountAd > 0;

        this.btnSweeping.node.setActive(canNormalSweep);
        this.btnSweepingAd.setActive(canAdSweep);

        if (canAdSweep) {
            this.btnSweepingAd.getComByChild(cc.Label, "count").string = cc.js.formatStr("今日剩余%d次", sweepCountAd);
        } else {
            this.btnSweeping.node.getComByChild(cc.Label, "count").string = cc.js.formatStr("今日剩余%d次", sweepCount);
            this.btnSweeping.node.getChildByName("energy").getComByChild(cc.Label, "Label").string = cc.js.formatStr("x%d", Manager.Manager.vo.switchVo.fightStamina);
        }
    }

    getRewardItem(cfg: any, index: number) {
        const rewards = ItemModel.default.instance.briefRewardArrTo(cfg.waveReward[index].splitInPairs(2));
        Notifier.Notifier.send(ListenID.ListenID.Item_GetReward, rewards);
        Manager.Manager.leveMgr.setLvRewardData(cfg.lvid, index);
        this.loadItem(cfg.lvid);
    }

    showRewardItem(cfg: any, index: number, position: cc.Vec2) {
        const listNode = this.talk.getChildByName("list");
        listNode.hideAllChildren();

        cfg.waveReward[index].splitInPairs(2).forEach((reward: any[], rewardIndex: number) => {
            const itemNode = listNode.children[rewardIndex] || cc.instantiate(listNode.children[0]).setAttribute({
                parent: listNode
            });

            itemNode.setAttribute({ active: true });

            const currencyConfig = Cfg.Cfg.CurrencyConfig.get(reward[0]);
            Manager.Manager.loader.loadSpriteToSprit(currencyConfig.icon, itemNode.getComByChild(cc.Sprite, "icon"));
            itemNode.getComByChild(cc.Label, "num").string = reward[1];
        });

        this.talk.setAttribute({
            position: position,
            active: true,
            scale: 0
        });

        cc.tween(this.talk)
            .to(0.1, { scale: 1 }, { easing: cc.easing.backOut })
            .start();
    }

    onClickDiffToggle(event: any, customEventData: string) {
        const toggleType = +customEventData;
        const currentCfg = this.curSelectPass;
        const isNormalMode = currentCfg.type === 1;

        if (toggleType === 1 && isNormalMode) {
            this.loadItem(currentCfg.unlockLvid[0]);
        } else if (toggleType === 0 && !isNormalMode) {
            const normalCfg = Cfg.Cfg.BagModeLv.getArray().find(cfg => {
                return cfg.unlockLvid && cfg.unlockLvid.includes(currentCfg.lvid);
            });
            this.loadItem(normalCfg.lvid);
        }
    }

    checkCanReward(cfg: any): number[] {
        if (!cfg) return [];

        const maxRound = Manager.Manager.leveMgr.getLvMaxRound(cfg.lvid);
        const canRewardIndexes: number[] = [];

        cfg.wave.forEach((wavePercent: number, index: number) => {
            const hasReward = Manager.Manager.leveMgr.getLvRewardData(cfg.lvid, index);
            if (cfg.wave[index] <= maxRound && !hasReward) {
                canRewardIndexes.push(index);
            }
        });

        return canRewardIndexes;
    }

    onOpen() {
        this.node.opacity = 0;
        this.mode.updateAdReward();
    }

    registerRedpoint() {
        if (this.boxtree && !this.redpointinit) {
            this.redpointinit = true;

            this.mode.treeNodes[1].forEach((nodeId: string, index: number) => {
                if (index > 0 && nodeId.includes("|")) {
                    this.boxtree?.SetCallBack(nodeId, nodeId, (count: number) => {
                        const boxIndex = nodeId.split("|")[0].split("_")[1];
                        this.boxnode.children[boxIndex].getChildByName("redpoint").active = count > 0;
                    });
                }
            });

            this.boxtree?.SetCallBack("fight_adreward", "fight_adreward", (count: number) => {
                this.rewardRedpoint.active = count > 0;
            });

            this.boxtree?.SetCallBack("fight_signin", "fight_signin", (count: number) => {
                this.signinRedpoint.active = count > 0;
            });

            this.mode.rewardmark.forEach((mark: string) => {
                this.boxtree?.ChangeRedPointCnt(mark, 1);
            });

            if (!this.signdata.dailyData.isAdSign) {
                this.boxtree?.ChangeRedPointCnt("fight_signin_ad", 1);
            }

            if (!this.signdata.dailyData.isSign) {
                this.boxtree?.ChangeRedPointCnt("fight_signin_normal", 1);
            }
        }
    }

    onShow() {
        this.resetview();
    }

    onShowFinish() {
        this.param?.showCb?.call(this.param, this.node);
        this.node.opacity = 255;
    }

    setInfo() {
        this.registerRedpoint();
        this.loadItem(Manager.Manager.leveMgr.CurChallengeLv);

        if (Manager.Manager.vo.userVo.guideIndex === 16) {
            Notifier.Notifier.send(ListenID.ListenID.Common_Guide_Anim, this.nodeArr[1], this.nodeArr[1], cc.v2(0, 0), null, true, () => {});
        }
    }

    resetview() {
        if ((window as any).wonderSdk.isByteDance) {
            const navBtn = cc.find("LeftBox/btn_cbl_tt", this.node);
            const canNavigate = Notifier.Notifier.call(CallID.CallID.Platform_CanNavigateTo);
            navBtn.active = canNavigate;

            const navReward = !Manager.Manager.vo.userVo.dailyData.ttNavReward &&
                            Notifier.Notifier.call(CallID.CallID.Platform_GetScene) === "021036";
            const fightTree = this.mode.treemap.fight;
            if (fightTree) {
                fightTree.ChangeRedPointCnt("fight_platformbar", navReward ? 1 : -fightTree.getRedpointCnt("fight_platformbar"));
            }

            const deskReward = !Manager.Manager.vo.knapsackVo.has("TTDestopReward") &&
                              Notifier.Notifier.call(CallID.CallID.Platform_CanAddDestop);
            cc.find("LeftBox/btn_desk_tt", this.node).active = deskReward;

            const hasDeskScene = !Manager.Manager.vo.knapsackVo.has("TTDestopReward") &&
                                Manager.Manager.vo.knapsackVo.has("TTScenc_destop") > 0;
            if (fightTree) {
                fightTree.ChangeRedPointCnt("fight_platformbar", hasDeskScene ? 1 : -fightTree.getRedpointCnt("fight_platformbar"));
            }

            Notifier.Notifier.send(ListenID.ListenID.Badge_Set, RBadgeModel.RBadge.Key.Fight_NavReward, !Manager.Manager.vo.userVo.dailyData.ttNavReward);
            Notifier.Notifier.send(ListenID.ListenID.Badge_Set, RBadgeModel.RBadge.Key.Fight_DeskReward, !Manager.Manager.vo.knapsackVo.has("TTDestopReward"));
        } else if ((window as any).wonderSdk.isBLMicro) {
            const navBtn = cc.find("LeftBox/btn_cbl_tt", this.node);
            const canNavigate = Notifier.Notifier.call(CallID.CallID.Platform_CanNavigateTo);
            navBtn.active = canNavigate;

            const navReward = !Manager.Manager.vo.userVo.dailyData.blNavReward &&
                            Notifier.Notifier.call(CallID.CallID.Platform_GetScene) === "021036";
            const fightTree = this.mode.treemap.fight;
            if (fightTree) {
                fightTree.ChangeRedPointCnt("fight_platformbar", navReward ? 1 : -fightTree.getRedpointCnt("fight_platformbar"));
            }

            Notifier.Notifier.send(ListenID.ListenID.Badge_Set, RBadgeModel.RBadge.Key.Fight_NavReward, !Manager.Manager.vo.userVo.dailyData.blNavReward);

            const deskReward = !Manager.Manager.vo.userVo.dailyData.blDeskReward;
            if (fightTree) {
                fightTree.ChangeRedPointCnt("fight_platformbar", deskReward ? 1 : -fightTree.getRedpointCnt("fight_platformbar"));
            }
            Notifier.Notifier.send(ListenID.ListenID.Badge_Set, RBadgeModel.RBadge.Key.Fight_DeskReward, deskReward);
        } else if ((window as any).wonderSdk.isKwai) {
            const ksBtn = cc.find("LeftBox/btn_ksset", this.node);
            const sdk = (window as any).wonderSdk.sdk;
            const ksCommonReward = Manager.Manager.vo.userVo.ksCommonReward;
            const ksRewardFromDesk = Manager.Manager.vo.userVo.dailyData.ksReward &&
                                   Notifier.Notifier.call(CallID.CallID.Ks_IsFromDestop);
            const addedToDesk = sdk._isaddtodestop && Manager.Manager.vo.userVo.dailyData.ksReward;
            const addedToCommon = sdk._isaddtocommonuse && !Manager.Manager.vo.userVo.ksCommonReward;
            const hasReward = addedToDesk || addedToCommon;

            ksBtn.getChildByName("redpoint").active = hasReward;

            const canShow = sdk._canAddtoCommon || sdk._canAddtodesk ||
                           (sdk._isaddtocommonuse && !ksCommonReward) ||
                           (ksRewardFromDesk && sdk._isaddtodestop);
            ksBtn?.setActive(canShow);
        }

        cc.find("LeftBox/brNewUserGift", this.node).setActive((window as any).wonderSdk.hasPay && !Manager.Manager.Shop.checkOrder(100));
        cc.find("LeftBox/brnSubscribeView", this.node).setActive((window as any).wonderSdk.hasPay);
        cc.find("LeftBox/brnSubscribeView/ggtipicon", this.node).setActive(
            Manager.Manager.Shop.checkSubscribeCanGet(Cfg.Cfg.PayShop.get(200)) ||
            Manager.Manager.Shop.checkSubscribeCanGet(Cfg.Cfg.PayShop.get(201))
        );

        const isSignComplete = this.signdata.adSignList.length === 7 && this.signdata.signIndex === 7;
        cc.find("LeftBox/btn_signIn", this.node).setActive(
            !isSignComplete &&
            Manager.Manager.leveMgr.vo.curPassLv >= Manager.Manager.vo.switchVo.dailysign
        );

        cc.find("RightBox/btn_task", this.node).setActive(
            Manager.Manager.leveMgr.vo.curPassLv >= Manager.Manager.vo.switchVo.task
        );

        cc.find("RightBox/btn_videogame", this.node).setActive(
            ModeBackpackHeroModel.default.instance.adGameSS !== ""
        );
    }

    onClickOpenTaskView() {
        Notifier.Notifier.send(ListenID.ListenID.Task_OpenMainView);
    }

    onOpenWin(event: any, customEventData: string) {
        const pageIndex = +customEventData;
        const openArgs = MVC.MVC.openArgs();
        openArgs.setParam({ pageIndex: pageIndex });
        UIManager.UIManager.Open("ui/setting/MoreGamesView", openArgs);
    }
}
