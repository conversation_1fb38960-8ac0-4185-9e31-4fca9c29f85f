import $2CallID from "./CallID";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2RBadgeModel from "./RBadgeModel";
import $2Game from "./Game";
import $2ItemModel from "./ItemModel";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_PrePare_Fight")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.SubPopup)
export default class M20_PrePare_Fight extends $2Pop.Pop {
    @property(cc.Node)
    nodeItem: cc.Node = null;

    @property(cc.Label)
    unlockinfo: cc.Label = null;

    @property(cc.Prefab)
    rewardPre: cc.Prefab = null;

    @property(cc.Button)
    btnSweeping: cc.Button = null;

    @property(cc.Node)
    btnSweepingAd: cc.Node = null;

    @property(cc.Node)
    boxnode: cc.Node = null;

    @property(cc.Node)
    pagelbtn: cc.Node = null;

    @property(cc.Node)
    pagerbtn: cc.Node = null;

    @property(cc.Node)
    leftnode: cc.Node = null;

    @property(cc.Node)
    rightnode: cc.Node = null;

    @property(cc.Node)
    rewardRedpoint: cc.Node = null;

    @property(cc.Node)
    signinRedpoint: cc.Node = null;

    @property(cc.Node)
    DiffSelect: cc.Node = null;

    @property(cc.Node)
    talk: cc.Node = null;

    constructor() {
        super();
        this.isontween = false;
        this.redpointinit = false;
    }

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    get signdata() {
        return this.mode.rVo;
    }

    get boxtree() {
        return this.mode.treemap && this.mode.treemap.fight;
    }

    changeListener(t: any) {
        super.changeListener(t);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.ResetView, this.setInfo, this);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Main_ResetView, this.resetview, this);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.ModeBack_RoundUnlock, this.onRoundUnlock, this);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Shop_InfoUpDate, this.resetview, this);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.FightHightRound, this.onFightHightRound, this);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_PrePare_Refresh_SweepView, this.setSweepView, this);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.PrePare_Fight_Skip, this.loadItem, this);
    }

    pageChange(e: any, t: any, o: any) {
        o === undefined && (o = .2);
            t = +t;
            const i = this.curSelectPass.lvid + t;
            i = $2Manager.Manager.leveMgr.getPassMainID(i);
            this.loadItem(i);
    }

    onFightHightRound() {
        this.loadItem(this.curSelectPass.lvid);
    }

    onRoundUnlock() {
        $2Manager.Manager.vo.userVo.guideIndex >= 17 && this.nodeArr[1].getChildByName("hand").setActive(true);
    }

    onBtn(e: any, t: any) {
        const o = this;
            const i = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView);
            $2Manager.Manager.leveMgr.PlayingLv = this.curSelectPass.lvid;
            i.onBtn(e, t, function () {
              $2UIManager.UIManager.Open("ui/ModeChains/M33_Pop_GameEnd", $2MVC.MVC.openArgs().setParam({
                isWin: true,
                cfg: o.curSelectPass
              }).setIsNeedLoading(false));
              o.loadItem(o.curSelectPass.lvid);
              $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 2);
            });
            t.includes("mode") && this.nodeArr[1].getChildByName("hand").setActive(false);
    }

    loadItem(e: any) {
        const t = this;
            const o = $2Cfg.Cfg.BagModeLv.getArray().filter(function (e) {
              return 1 == e.type;
            }).lastVal.lvid;
            const i = cc.misc.clampf($2Manager.Manager.leveMgr.CurChallengeLv + 2, 5, o);
            const n = $2Manager.Manager.leveMgr.getPassMainID(e);
            this.talk.setAttribute({
              active: false,
              scale: 0
            });
            const r = this.curSelectPass = $2Cfg.Cfg.BagModeLv.get(e);
            const a = 1 == r.type;
            const c = $2Manager.Manager.leveMgr.getLvMaxRound(e);
            if (a) {
              this.setSweepView();
            } else {
              this.btnSweeping.node.setActive(false);
            }
            const u = $2GameSeting.GameSeting.getDiffDef(r.type);
            this.DiffSelect.getComByChild(cc.RichText).text = cc.js.formatStr("<outline color=black width=3>难度:<color=%s>%s</c>", u.colorStr, u.name);

            const passImg = this.nodeItem.getComByChild(cc.Sprite, "passImg");

            if (r.lvicon?.length) {
              let lvNumber = r.lvicon.split('lv')[1];  // 获取"lv"后面的部分
              if (lvNumber == "6" || lvNumber == "16") lvNumber = "5";
              const passSke = this.nodeItem.getComByChild(sp.Skeleton, "zhangjie");
              passSke.setAnimation(0, "zhang" + lvNumber, true);
              $2Manager.Manager.loader.loadSpriteToSprit(r.lvicon, passImg, this.node);
            }

            this.nodeItem.getComByChild(cc.Label, "title").setAttribute({
              // string: cc.js.formatStr("%s.%s", r.lvid % 1e3, r.name)
              string: cc.js.formatStr("%s", r.name)
            }).node.setAttribute({
              color: cc.Color.WHITE
            });
            $2Manager.Manager.loader.loadSpriteToSprit("v1/images/bg/" + ["hall", "hall", "hall"][r.type - 1], this.node.getComByPath(cc.Sprite, "maskbg"), this.node);
            cc.tween(this.nodeItem).stopLast().to(.1, {
              scale: 1.01
            }).to(.1, {
              scale: 1
            }).start();
            r.waveReward.forEach(function (o, i) {
              const n = t.boxnode.children[i] || cc.instantiate(t.boxnode.children[0]).setAttribute({
                parent: t.boxnode
              });
              const a = $2Manager.Manager.leveMgr.getLvRewardData(e, i);
              const s = r.wave[i] <= c && !a;
              n.getComByChild(cc.Label, "wave").string = s ? "可领取" : cc.js.formatStr("%d%", r.wave[i]);
              // $2Manager.Manager.loader.loadSpriteToSprit("img/ModeBackpackHero/boxicon" + ["", "2", "3"][i] + "_0" + (a ? "2" : "1"), n.getComByChild(cc.Sprite, "icon"));
              $2Manager.Manager.loader.loadSpriteToSprit("v1/images/icon/boxicon" + ["1", "2", "3"][i] + "_0" + (a ? "2" : "1"), n.getComByChild(cc.Sprite, "icon"));
              n.getChildByName("redpoint").setActive(s);
              n.targetOff(t);
              cc.Tween.stopAllByTarget(n);
              s && cc.tween(n).sequence(cc.tween().to(.3, {
                scale: 1
              }, {
                easing: "sineInOut"
              }), cc.tween().to(.3, {
                scale: 1.1
              }, {
                easing: "sineInOut"
              })).repeatForever().start();
              n.on(cc.Node.EventType.TOUCH_END, function () {
                if (s) {
                  t.getRewardItem(r, i);
                } else {
                  t.showRewardItem(r, i, n.wordPos);
                }
              }, t);
            });
            const p = $2Manager.Manager.leveMgr.vo.lvIdUnlock.includes(e);
            if (a) {
              p || (this.unlockinfo.string = cc.js.formatStr("通关第%d章后解锁", e - 1));
            } else if (r.type == $2GameSeting.GameSeting.DiffType.Hard && $2Manager.Manager.leveMgr.vo.curPassLv < 6) {
              p = false;
              this.unlockinfo.string = cc.js.formatStr("通关第%d章后解锁", 6);
            } else if (r.type == $2GameSeting.GameSeting.DiffType.Hell && $2Manager.Manager.leveMgr.vo.curPassLv < 8) {
              p = false;
              this.unlockinfo.string = cc.js.formatStr("通关第%d章后解锁", 8);
            } else {
              p || (this.unlockinfo.string = cc.js.formatStr("通关%s关卡后解锁", $2GameSeting.GameSeting.getDiffDef(r.type - 1).name));
            }
            this.btnSweepingAd.parent.setActive(p);
            this.nodeArr[1].active = p;
            this.nodeItem.getChildByName("lock").active = !p;
            const f = $2Manager.Manager.leveMgr.checkLvIsPass(e);
            const d = this.nodeItem.getChildByName("unlockinfo").getComponent(cc.Label);
            const g = 0 == c ? "未通关" : cc.js.formatStr("历史最佳") + " " + c + "%";
            d.string = f ? "已通关" : g;
            this.labelArr[0].string = "x" + $2Manager.Manager.vo.switchVo.fightStamina;
            // if (a) {
            this.pagelbtn.active = e > 1;
            this.pagerbtn.active = e < i;
            // }
            cc.tween(this.nodeArr[1]).stopLast().set({
              scale: 1
            }).start();
            f || cc.tween(this.nodeArr[1]).sequence(cc.tween().to(.7, {
              scale: 1.05
            }, {
              easing: "sineInOut"
            }), cc.tween().to(.7, {
              scale: 1
            }, {
              easing: "sineInOut"
            })).repeatForever().start();
            this.nodeItem.getChildByName("passImg").opacity = p ? 255 : 100;
            this.nodeItem.getChildByName("zhangjie").setActive(p);
            this.unlockinfo.node.active = !p;
            const y = this.checkCanReward($2Cfg.Cfg.BagModeLv.find({
              lvid: n + 1
            }));
            this.rightnode.setActive(y.length > 0);
            const m = this.checkCanReward($2Cfg.Cfg.BagModeLv.find({
              lvid: n - 1
            }));
            this.leftnode.setActive(m.length > 0);
            $2Manager.Manager.leveMgr.vo.curPassLv <= 5 && $2Manager.Manager.vo.userVo.guideIndex >= 17 && this.nodeArr[1].getChildByName("hand").setActive(r.lvid == $2Manager.Manager.leveMgr.CurChallengeLv);
    }

    selectDiff(e: any) {
        const t = this.curSelectPass.lvid % 1e3;
            this.loadItem(1e3 * (e - 1) + t);
    }

    setSweepView() {
        const e = $2Manager.Manager.leveMgr.vo.dailyData.sweep_count;
            const t = $2Manager.Manager.leveMgr.vo.dailyData.sweep_count_ad;
            const o = $2Manager.Manager.leveMgr.checkLvIsPass(this.curSelectPass.lvid);
            const i = 1 == this.curSelectPass.type;
            if (!o || !i) {
              this.btnSweeping.node.setActive(false);
              return void this.btnSweepingAd.setActive(false);
            }
            console.log($2Manager.Manager.vo.switchVo.lvSweep);
            const n = e > 0;
            console.log($2Manager.Manager.leveMgr.vo.dailyData.isADSweepNum);
            const r = e <= 0 && t > 0;
            this.btnSweeping.node.setActive(n);
            this.btnSweepingAd.setActive(r);
            if (r) {
              this.btnSweepingAd.getComByChild(cc.Label, "count").string = cc.js.formatStr("今日剩余%d次", t);
            } else {
              this.btnSweeping.node.getComByChild(cc.Label, "count").string = cc.js.formatStr("今日剩余%d次", e);
              this.btnSweeping.node.getChildByName("energy").getComByChild(cc.Label, "Label").string = cc.js.formatStr("x%d", $2Manager.Manager.vo.switchVo.fightStamina);
            }
    }

    getRewardItem(e: any, t: any) {
        const o = $2ItemModel.default.instance.briefRewardArrTo(e.waveReward[t].splitInPairs(2));
            $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, o);
            $2Manager.Manager.leveMgr.setLvRewardData(e.lvid, t);
            this.loadItem(e.lvid);
    }

    showRewardItem(e: any, t: any, o: any) {
        const i = this.talk.getChildByName("list");
            i.hideAllChildren();
            e.waveReward[t].splitInPairs(2).forEach(function (e, t) {
              const o = i.children[t] || cc.instantiate(i.children[0]).setAttribute({
                parent: i
              });
              o.setAttribute({
                active: true
              });
              const n = $2Cfg.Cfg.CurrencyConfig.get(e[0]);
              $2Manager.Manager.loader.loadSpriteToSprit(n.icon, o.getComByChild(cc.Sprite, "icon"));
              o.getComByChild(cc.Label, "num").string = e[1];
            });
            this.talk.setAttribute({
              position: o,
              active: true,
              scale: 0
            });
            cc.tween(this.talk).to(.1, {
              scale: 1
            }, {
              easing: cc.easing.backOut
            }).start();
    }

    onClickDiffToggle(e: any, t: any) {
        const o = +t;
            const i = this.curSelectPass;
            const n = i.type = 1;
            if (1 == o && n) {
              this.loadItem(i.unlockLvid[0]);
            } else if (0 == o && !n) {
              const r = $2Cfg.Cfg.BagModeLv.getArray().find(function (e) {
                let t: any;
                if (null === (t = e.unlockLvid) || t === undefined) {
                  return undefined;
                } else {
                  return t.includes(i.lvid);
                }
              });
              this.loadItem(r.lvid);
            }
    }

    checkCanReward(e: any) {
        if (!e) {
              return [];
            }
            const t = $2Manager.Manager.leveMgr.getLvMaxRound(e.lvid);
            const o = [];
            e.wave.forEach(function (i, n) {
              const r = $2Manager.Manager.leveMgr.getLvRewardData(e.lvid, n);
              e.wave[n] <= t && !r && o.push(n);
            });
            return o;
    }

    onOpen() {
        this.node.opacity = 0;
            this.mode.updateAdReward();
    }

    registerRedpoint() {
        let e: any;
            let t: any;
            let o: any;
            let i: any;
            const n = this;
            if (this.boxtree && !this.redpointinit) {
              this.redpointinit = true;
              this.mode.treeNodes[1].forEach(function (e, t) {
                let o: any;
                t > 0 && e.includes("|") && (null === (o = n.boxtree) || o === undefined || o.SetCallBack(e, e, function (t) {
                  n.boxnode.children[e.split("|")[0].split("_")[1]].getChildByName("redpoint").active = t > 0;
                }));
              });
              null === (e = this.boxtree) || e === undefined || e.SetCallBack("fight_adreward", "fight_adreward", function (e) {
                n.rewardRedpoint.active = e > 0;
              });
              null === (t = this.boxtree) || t === undefined || t.SetCallBack("fight_signin", "fight_signin", function (e) {
                n.signinRedpoint.active = e > 0;
              });
              this.mode.rewardmark.forEach(function (e) {
                let t: any;
                null === (t = n.boxtree) || t === undefined || t.ChangeRedPointCnt(e, 1);
              });
              this.signdata.dailyData.isAdSign || null === (o = this.boxtree) || o === undefined || o.ChangeRedPointCnt("fight_signin_ad", 1);
              this.signdata.dailyData.isSign || null === (i = this.boxtree) || i === undefined || i.ChangeRedPointCnt("fight_signin_normal", 1);
            }
    }

    onShow() {
        this.resetview();
    }

    onShowFinish() {
        let e: any;
            let t: any;
            null === (t = (e = this.param).showCb) || t === undefined || t.call(e, this.node);
            this.node.opacity = 255;
    }

    setInfo() {
        this.registerRedpoint();
            this.loadItem($2Manager.Manager.leveMgr.CurChallengeLv);
            16 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Anim, this.nodeArr[1], this.nodeArr[1], cc.v2(0, 0), null, true, function () { });
    }

    resetview() {
        let e: any;
            if ((window as any).wonderSdk.isByteDance) {
              e = cc.find("LeftBox/btn_cbl_tt", this.node);
              const t = $2Notifier.Notifier.call($2CallID.CallID.Platform_CanNavigateTo);
              e.active = t;
              const o = !$2Manager.Manager.vo.userVo.dailyData.ttNavReward && "021036" == $2Notifier.Notifier.call($2CallID.CallID.Platform_GetScene);
              r = this.mode.treemap.fight;
              if (r) {
                r.ChangeRedPointCnt("fight_platformbar", o ? 1 : -r.getRedpointCnt("fight_platformbar"));
              }
              const i = !$2Manager.Manager.vo.knapsackVo.has("TTDestopReward") && $2Notifier.Notifier.call($2CallID.CallID.Platform_CanAddDestop);
              cc.find("LeftBox/btn_desk_tt", this.node).active = i;
              const n = !$2Manager.Manager.vo.knapsackVo.has("TTDestopReward") && $2Manager.Manager.vo.knapsackVo.has("TTScenc_destop") > 0;
              r && r.ChangeRedPointCnt("fight_platformbar", n ? 1 : -r.getRedpointCnt("fight_platformbar"));
              $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Fight_NavReward, !$2Manager.Manager.vo.userVo.dailyData.ttNavReward);
              $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Fight_DeskReward, !$2Manager.Manager.vo.knapsackVo.has("TTDestopReward"));
            } else if ((window as any).wonderSdk.isBLMicro) {
              let r: any;
              e = cc.find("LeftBox/btn_cbl_tt", this.node);
              t = $2Notifier.Notifier.call($2CallID.CallID.Platform_CanNavigateTo);
              e.active = t;
              o = !$2Manager.Manager.vo.userVo.dailyData.blNavReward && "021036" == $2Notifier.Notifier.call($2CallID.CallID.Platform_GetScene);
              r = this.mode.treemap.fight;
              if (r) {
                r.ChangeRedPointCnt("fight_platformbar", o ? 1 : -r.getRedpointCnt("fight_platformbar"));
              }
              $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Fight_NavReward, !$2Manager.Manager.vo.userVo.dailyData.blNavReward);
              n = !$2Manager.Manager.vo.userVo.dailyData.blDeskReward;
              r && r.ChangeRedPointCnt("fight_platformbar", n ? 1 : -r.getRedpointCnt("fight_platformbar"));
              $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Set, $2RBadgeModel.RBadge.Key.Fight_DeskReward, n);
            } else if ((window as any).wonderSdk.isKwai) {
              e = cc.find("LeftBox/btn_ksset", this.node);
              const s = (window as any).wonderSdk.sdk;
              const u = $2Manager.Manager.vo.userVo.ksCommonReward;
              const p = $2Manager.Manager.vo.userVo.dailyData.ksReward && $2Notifier.Notifier.call($2CallID.CallID.Ks_IsFromDestop);
              const d = s._isaddtodestop && $2Manager.Manager.vo.userVo.dailyData.ksReward;
              const y = s._isaddtocommonuse && !$2Manager.Manager.vo.userVo.ksCommonReward;
              const m = d || y;
              e.getChildByName("redpoint").active = m;
              const v = s._canAddtoCommon || s._canAddtodesk || s._isaddtocommonuse && !u || p && s._isaddtodestop;
              null == e || e.setActive(v);
            }
            // cc.find("LeftBox/brNewUserGift", this.node).setActive((window as any).wonderSdk.hasPay && !$2Manager.Manager.Shop.checkOrder(100));
            // cc.find("LeftBox/brnSubscribeView", this.node).setActive((window as any).wonderSdk.hasPay);
            // cc.find("LeftBox/brnSubscribeView/ggtipicon", this.node).setActive($2Manager.Manager.Shop.checkSubscribeCanGet($2Cfg.Cfg.PayShop.get(200)) || $2Manager.Manager.Shop.checkSubscribeCanGet($2Cfg.Cfg.PayShop.get(201)));
            // const M = 7 == this.signdata.adSignList.length && 7 == this.signdata.signIndex;
            // cc.find("LeftBox/btn_signIn", this.node).setActive(!M && $2Manager.Manager.leveMgr.vo.curPassLv >= $2Manager.Manager.vo.switchVo.dailysign);
            // cc.find("RightBox/btn_task", this.node).setActive($2Manager.Manager.leveMgr.vo.curPassLv >= $2Manager.Manager.vo.switchVo.task);
            // cc.find("RightBox/btn_videogame", this.node).setActive("" != $2ModeBackpackHeroModel.default.instance.adGameSS);
    }

    onClickOpenTaskView() {
        $2Notifier.Notifier.send($2ListenID.ListenID.Task_OpenMainView);
    }

    onOpenWin(e: any, t: any) {
        const o = +t;
            const i = $2MVC.MVC.openArgs();
            i.setParam({
              pageIndex: o
            });
            $2UIManager.UIManager.Open("ui/setting/MoreGamesView", i);
    }

}
