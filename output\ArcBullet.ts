import $2GameUtil from "./GameUtil";
import $2Game from "./Game";
import $2BulletBase from "./BulletBase";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("Bullet/ArcBullet")
export default class ArcBullet extends $2BulletBase.default {
    constructor() {
        super();
        this.lastPos = cc.v2();
        this._dt = 0;
    }

    init() {
        super.init();
            const t = $2GameUtil.GameUtil.GetAngle(this.vo.startPos, this.vo.targetPos) + 90;
            const o = [];
            o.push(this.vo.startPos);
            o.push(this.vo.targetPos.add(this.vo.startPos).div(2).add($2GameUtil.GameUtil.AngleAndLenToPos(t - 90 * (this.vo.targetPos.x > 0 ? -1 : 1), 500 + $2Game.Game.random(-100, 100))));
            o.push(this.vo.targetPos);
            const i = cc.Vec2.distance(this.vo.startPos, this.vo.targetPos) / this.maxSpeed;
            $2Game.Game.tween(this.node).bezierTo(i, o[0], o[1], o[2]).by(300 / this.maxSpeed, {
              position: o[2].sub(o[1]).normalize().mul(500)
            }).start();
    }

}
