import $2CallID from "./$2CallID";
import $2Cfg from "./$2Cfg";
import $2MVC from "./$2MVC";
import $2Pop from "./$2Pop";
import $2Notifier from "./$2Notifier";
import $2Manager from "./$2Manager";
import $2EaseScaleTransition from "./$2EaseScaleTransition";
import $2GameUtil from "./$2GameUtil";
import $2AlertManager from "./$2AlertManager";
import $2Game from "./$2Game";
import $2ModeBackpackHeroModel from "./$2ModeBackpackHeroModel";
import $2M20Gooditem from "./$2M20Gooditem";

const { ccclass, property, menu } = cc._decorator;

interface GoodItemData {
    count: number;
    path: string;
    bgpath: string;
    isfrag: boolean;
}

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_Pop_GetBox")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup)
@$2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Popup)
@$2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)
export default class M20_Pop_GetBox extends $2Pop.Pop {
    @property(cc.Label)
    boxname: cc.Label = null;

    @property(cc.Label)
    tips: cc.Label = null;

    @property(cc.Node)
    box: cc.Node = null;

    @property(cc.Node)
    shine: cc.Node = null;

    @property(cc.Sprite)
    boximg: cc.Sprite = null;

    @property(cc.Node)
    rewardContainer: cc.Node = null;

    clicktims: number = 0;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    changeListener(isAdd: boolean) {
        super.changeListener(isAdd);
    }

    onOpen() {
        $2Manager.Manager.loader.loadSpriteToSprit(
            "v1/images/icon/good" + this.param.data.id.toString().slice(0, 3) + "01", 
            this.boximg
        );
        
        this.tips.string = "打开宝箱";
        
        cc.tween(this.shine)
            .by(0.2, { angle: 10 })
            .repeatForever()
            .start();
        
        this.boxname.string = this.param?.data.title;
        
        cc.tween(this.box)
            .set({ y: 1000 })
            .to(0.2, { y: 0 })
            .start();
        
        cc.tween(this.box)
            .delay(0.2)
            .by(0.2, { y: 100 })
            .by(0.2, { y: -100 })
            .delay($2GameUtil.GameUtil.random(1, 3))
            .union()
            .repeatForever()
            .start();
    }

    async getReward() {
        if (this.clicktims >= 1) {
            if (this.clicktims === 2) {
                this.close();
            }
            return;
        }

        const fragments = this.mode.fragments;
        this.clicktims++;
        this.tips.string = "";
        cc.Tween.stopAllByTarget(this.box);
        
        const shakeAngle = $2GameUtil.GameUtil.random(10, 30);
        cc.tween(this.box)
            .to(0.2, { angle: -shakeAngle })
            .to(0.2, { angle: shakeAngle })
            .to(0.1, { angle: 0 })
            .delay(0.3)
            .to(0.5, { opacity: 0 })
            .call(() => {
                this.rewardContainer.setActive(true);
                this.tips.string = "点击关闭";
                this.clicktims = 2;
            })
            .start();

        if (!this.param.data) {
            return;
        }

        const goodItemPrefab = await $2Manager.Manager.loader.loadPrefab("ui/ModeBackpackHero/goodItem");
        let delayIndex = 0;

        this.param.data.boxReward.forEach((reward: any[]) => {
            const count = reward[1];
            const id = reward[0];
            
            const itemData: GoodItemData = {
                count: count,
                path: "",
                bgpath: "",
                isfrag: false
            };

            if (fragments.includes(id)) {
                this.mode.getRandomFragById(reward).forEach((fragCount: number, fragId: number) => {
                    const roleUnlock = $2Cfg.Cfg.RoleUnlock.get(fragId);
                    itemData.path = roleUnlock.icon;
                    itemData.count = fragCount;
                    itemData.bgpath = "v1/images/bg/bg_icon_0" + roleUnlock.rarity;
                    itemData.isfrag = true;
                    
                    this.mode.addFragment(roleUnlock.id, fragCount);
                    
                    const itemNode = cc.instantiate(goodItemPrefab);
                    itemNode.setParent(this.rewardContainer);
                    itemNode.getComponent($2M20Gooditem.default).setdata(itemData);
                    
                    cc.tween(itemNode)
                        .set({ opacity: 0 })
                        .delay(1.2 + 0.1 * delayIndex)
                        .to(0.2, { opacity: 255 })
                        .start();
                    
                    delayIndex++;
                });
            } else {
                const itemNode = cc.instantiate(goodItemPrefab);
                itemNode.setParent(this.rewardContainer);
                
                const currencyConfig = $2Cfg.Cfg.CurrencyConfig.get(id);
                itemData.path = currencyConfig.icon;
                
                $2Manager.Manager.vo.knapsackVo.addGoods(id, count);
                $2AlertManager.AlertManager.showNormalTips("获得" + currencyConfig.name + "x" + count);
                
                itemNode.getComponent($2M20Gooditem.default).setdata(itemData);
                
                cc.tween(itemNode)
                    .set({ opacity: 0 })
                    .delay(1.2 + 0.1 * delayIndex)
                    .to(0.2, { opacity: 255 })
                    .start();
                
                delayIndex++;
            }
        });
    }

    onBtn(event: any, customEventData: string) {
        $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView).onBtn(event, customEventData);
    }

    onClose() {
        cc.Tween.stopAllByTarget(this.shine);
        cc.Tween.stopAllByTarget(this.box);
        this.param?.cb?.call(this.param);
    }

    setInfo() {}
}
