const fs = require('fs');
const path = require('path');

function fixImportsInFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // 修正导入语句：保留变量名的 $2，但移除路径中的 $2
        content = content.replace(/import (\$2\w+) from "\.\/([\$2]*)(\w+)";/g, (match, varName, prefix, fileName) => {
            // 如果文件名前有 $2，移除它
            const cleanFileName = fileName.replace(/^\$2/, '');
            return `import ${varName} from "./${cleanFileName}";`;
        });
        
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✅ 修正: ${path.basename(filePath)}`);
        return true;
    } catch (error) {
        console.error(`❌ 错误: ${path.basename(filePath)}:`, error.message);
        return false;
    }
}

function fixImportsInDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
        console.error(`目录不存在: ${dirPath}`);
        return;
    }
    
    const files = fs.readdirSync(dirPath).filter(file => file.endsWith('.ts'));
    let fixedCount = 0;
    
    console.log(`🔧 开始修正 ${files.length} 个文件的导入语句...\n`);
    
    files.forEach(file => {
        const filePath = path.join(dirPath, file);
        if (fixImportsInFile(filePath)) {
            fixedCount++;
        }
    });
    
    console.log(`\n🎉 修正完成! 成功: ${fixedCount}/${files.length}`);
}

// 使用示例
if (require.main === module) {
    const args = process.argv.slice(2);
    const targetDir = args[0] || './output';
    
    console.log(`📁 目标目录: ${targetDir}\n`);
    fixImportsInDirectory(targetDir);
}

module.exports = { fixImportsInFile, fixImportsInDirectory };
