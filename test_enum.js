const FinalJSToTSConverter = require('./final_converter');
const fs = require('fs');

// 测试单个文件的枚举转换
const converter = new FinalJSToTSConverter();

// 读取文件内容
const jsContent = fs.readFileSync('./scripts/M20_PrePare_Equip.js', 'utf8');

// 转换
const tsContent = converter.convertJSToTS(jsContent);

if (tsContent) {
    // 写入文件
    fs.writeFileSync('./M20_PrePare_Equip_with_constructor.ts', tsContent, 'utf8');
    console.log('✅ 转换成功！');
    
    // 显示前50行
    const lines = tsContent.split('\n');
    console.log('\n前50行预览:');
    console.log(lines.slice(0, 50).join('\n'));
} else {
    console.log('❌ 转换失败');
}
