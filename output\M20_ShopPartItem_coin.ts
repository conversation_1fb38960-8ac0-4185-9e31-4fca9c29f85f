import $2Cfg from "./$2Cfg";
import $2CurrencyConfigCfg from "./$2CurrencyConfigCfg";
import $2GameUtil from "./$2GameUtil";
import $2M20_PartItem from "./$2M20_PartItem";
import $2M20_ShopPartItem from "./$2M20_ShopPartItem";

const { ccclass } = cc._decorator;

@ccclass
export default class M20_ShopPartItem_coin extends $2M20_ShopPartItem.default {
    getList(): any[] {
        return [...$2GameUtil.GameUtil.getRandomInArray($2Cfg.Cfg.BagShopItem.filter({
            type: $2CurrencyConfigCfg.CurrencyConfigDefine.Coin
        }), 3)];
    }

    resetView() {
        super.resetView();
        for (let i = 0; i < this.content.length; i++) {
            const itemData = this.content[i];
            const itemNode = this.contentnode.children[i] || cc.instantiate(this.cloneitem);
            itemNode.setAttribute({ parent: this.contentnode });
            itemNode.getComponent($2M20_PartItem.default).setdata(itemData, false, 1);
        }
    }
}
