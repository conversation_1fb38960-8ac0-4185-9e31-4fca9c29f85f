import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2GameUtil from "./GameUtil";
import $2M20_PartItem from "./M20_PartItem";
import $2M20_ShopPartItem from "./M20_ShopPartItem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_ShopPartItem_coin extends $2M20_ShopPartItem.default {
    getList() {
        return [...$2GameUtil.GameUtil.getRandomInArray($2Cfg.Cfg.BagShopItem.filter({
              type: $2CurrencyConfigCfg.CurrencyConfigDefine.Coin
            }], 3));
    }

    resetView() {
        super.resetView();
            for (const t = 0; t < this.content.length; t++) {
              const o = this.content[t];
              const i = this.contentnode.children[t] || cc.instantiate(this.cloneitem);
              i.setAttribute({
                parent: this.contentnode
              });
              i.getComponent($2M20_PartItem.default).setdata(o, false, 1);
            }
    }

}
