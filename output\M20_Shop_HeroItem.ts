import $2CallID from "./CallID";
import $2L<PERSON>enID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_Shop_HeroItem extends cc.Component {
    @property(cc.Label)
    goodname: cc.Label = null;

    @property(cc.Label)
    oncecost: cc.Label = null;

    @property(cc.Label)
    weekcost: cc.Label = null;

    constructor() {
        super();
        this._canclick = true;
    }

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    setdata(e: any) {
        this.oncecost.string = e.costVal[1];
            this.weekcost.string = e.costVal[2];
    }

    getfrag(e: any, t: any) {
        const o = this;
            if (this._canclick) {
              this._canclick = false;
              this.scheduleOnce(function () {
                o._canclick = true;
              }, 1);
              t = Number(t);
              for (const i = 0; i < t; i++) {
                const n = Math.random() > .5 ? "A" : "B";
                const r = "A" == n ? $2Manager.Manager.vo.switchVo.heroAfragmentWeight : $2Manager.Manager.vo.switchVo.heroBfragmentWeight;
                const a = $2GameUtil.GameUtil.getRandomByWeightInArray(r, 1)[0];
                const s = $2Cfg.Cfg.RoleUnlock.find({
                  type: 3,
                  rarity: n
                });
                const c = $2Cfg.Cfg.RoleLv.find({
                  roleId: s.id
                });
                const p = [{
                  id: s.id,
                  num: a,
                  type: c.type,
                  rarity: $2Cfg.Cfg.CurrencyConfig.get(c.type).rarity
                }];
                $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_GameRewardView", $2MVC.MVC.openArgs().setParam({
                  data: p
                }));
                this.mode.addFragment(c.roleId, a);
                this.mode.dailyAdpack.addGoods("fragadcount" + c.roleId);
              }
            }
    }

    sendEvent(e: any, t: any) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "reward_btn", Object.assign({
              Type: e,
              Scene: t
            }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}));
    }

}
