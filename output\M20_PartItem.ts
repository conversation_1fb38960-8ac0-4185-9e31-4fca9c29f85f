import $2CallID from "./CallID";
import $2GameSeting from "./GameSeting";
import $2L<PERSON>enID from "./ListenID";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2NotifyID from "./NotifyID";
import $2Manager from "./Manager";
import $2Time from "./Time";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";
import $2RBadgeModel from "./RBadgeModel";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20Gooditem from "./M20Gooditem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_PartItem extends cc.Component {
    @property(cc.Label)
    goodname: cc.Label = null;

    @property(cc.Sprite)
    bg: cc.Sprite = null;

    @property(cc.Label)
    goodcost: cc.Label = null;

    @property(cc.RichText)
    gooddesc: cc.RichText = null;

    @property(cc.Node)
    goodnode: cc.Node = null;

    @property(cc.Node)
    rewardnode: cc.Node = null;

    @property(cc.Node)
    typeicon: cc.Node = null;

    @property(cc.Node)
    discountnode: cc.Node = null;

    @property(cc.RichText)
    adUnlockCount: cc.RichText = null;

    @property(cc.Label)
    coinUnlockCount: cc.Label = null;

    @property(cc.Button)
    contentClick: cc.Button = null;

    @property(cc.Button)
    adClick: cc.Button = null;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    setdata(e: any, t: any, o: any) {
        this.data = e;
            this.curdiscount = o;
            this.ishigh = t;
            this.resetConst();
            this.updateUI(e, t, o);
            if (200 == this.data.id) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Add, this.node, $2RBadgeModel.RBadge.Key.Shop_FreeCoin);
            } else if (300 == this.data.id) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Add, this.node, $2RBadgeModel.RBadge.Key.Shop_FreeDiamond);
            } else {
              $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Add, this.node, null);
            }
    }

    resetConst() {
        const e = this;
            this.consList.length = 0;
            this.data.costType.forEach(function (t, o) {
              e.consList.push(e.getConst(t, e.data.costVal[o]));
            });
    }

    showreward() {
        const e = this;
            const t = 0 == this.rewardnode.scaleY ? 1 : 0;
            const o = 1 == t ? 255 : 0;
            cc.tween(this.rewardnode).to(.1, {
              scaleY: t,
              opacity: o
            }).start();
            const i = this.rewardnode.children[0].children[1];
            const n = this.rewardnode.children[0].children[1].children[0];
            this.data.boxReward.forEach(function (t, o) {
              const r = t[1];
              const a = t[0];
              const s = i.children[o] || cc.instantiate(n);
              s.setParent(i);
              s.active = true;
              let l: any;
              const p = $2Cfg.Cfg.CurrencyConfig.find({
                id: a
              });
              l = e.mode.fragments.includes(a) ? $2GameSeting.GameSeting.getRarity(e.mode.buffmap[p.id]).framgimg : p.icon;
              $2Manager.Manager.loader.loadSpriteToSprit(l, s.getChildByName("icon").getComponent(cc.Sprite), e.node);
              s.getChildByName("num").getComponent(cc.Label).string = "x" + r;
            });
    }

    updateUI(e: any, t: any, o: any) {
        this.goodname.string = e.title;
            this.resetUI();
            this.updateDiscountUI(o);
            this.updateDescription(e);
            this.updateCostUI(e, t);
            // e.bgpath && $2Manager.Manager.loader.loadSpriteToSprit(e.bgpath, this.bg, this.node);
            this.loadGoodItem(e);
    }

    resetUI() {
        let e: any;
            let t: any;
            let o: any;
            let i: any;
            let n: any;
            null === (t = null === (e = this.adClick) || e === undefined ? undefined : e.node.getChildByName("bgdisable")) || t === undefined || t.setActive(false);
            null === (i = null === (o = this.contentClick) || o === undefined ? undefined : o.node.getChildByName("bgdisable")) || i === undefined || i.setActive(false);
            null === (n = this.typeicon) || n === undefined || n.children.forEach(function (e) {
              return e.setActive(false);
            });
    }

    updateDiscountUI(e: any) {
        if (this.discountnode) {
              this.discountnode.active = 1 != e;
              this.discountnode.getComponentInChildren(cc.Label).string = cc.js.formatStr("%d折", 10 * e);
            }
    }

    updateDescription(e: any) {
        if (e.costType[0] !== $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio || e.type != $2CurrencyConfigCfg.CurrencyConfigDefine.Coin && e.type != $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond) {
              this.gooddesc.text = "" != e.desc ? cc.js.formatStr(e.desc) : cc.js.formatStr("<color=#474747>x%d</color>", this.getCount(e));
            }
            // else {
            //   this.gooddesc.text = cc.js.formatStr("<color=#474747>x%d</color>", this.getCount(e));
            // }
    }

    updateCostUI(e: any, t: any) {
        if (t) {
              this.handleHighLevelBox(e);
            } else {
              this.handleNormalItem(e);
            }
    }

    getConst(e: any, t: any) {
        const o = {
              type: e,
              val: t
            };
            switch (o.type) {
              case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
                if ($2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.advboxkey) && this.data.id >= 10101 && this.data.id <= 10110) {
                  o.type = $2CurrencyConfigCfg.CurrencyConfigDefine.advboxkey;
                  o.val = 1;
                }
            }
            return o;
    }

    handleHighLevelBox(e: any) {
        const t = this;
            const o = false;
            const i = false;
            e.costType.forEach(function (n, r) {
              if (n == $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
                o = 0 == t.checkDailyBuyLimit($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio, t.adClick, false);
                const a = t.adClick.node.getChildByName("desc_ads");
                if (o) {
                  a.active = false;
                } else {
                  a.active = true;
                  if (t.isNoAdLimit) {
                    a.getComponent(cc.Label).string = " 免 费 ";
                  } else {
                    a.getComponent(cc.Label).string = cc.js.formatStr("免费(%d)", t.curbuyCount);
                  }
                }
              } else if (n == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond) {
                t.coinUnlockCount.string = t.consList[r].val;
                t.coinDefine = e.costType[r];
                i = 0 == t.checkDailyBuyLimit(t.coinDefine, t.adClick, false);
                $2Manager.Manager.loader.loadSpriteToSprit($2Cfg.Cfg.CurrencyConfig.get(t.consList[r].type).icon, t.coinUnlockCount.node.parent.getComByPath(cc.Sprite, "icon_ads"), t.node);
                t.coinUnlockCount.node.parent.parent.getComponent(cc.Button).clickEvents[0].customEventData = t.consList[r].type + "";
              }
            });
            o && i && this.setDisable(this.adClick.node);
    }

    handleNormalItem(e: any) {
        this.coinDefine = e.costType[0];
            if (this.coinDefine == $2CurrencyConfigCfg.CurrencyConfigDefine.purchases) {
              this.goodcost.string = $2Manager.Manager.Shop.getFromPrice($2Cfg.Cfg.PayShop.get(e.payShopId));
            } else {
              this.goodcost.string = Math.floor(e.costVal[0] * this.curdiscount) + "";
            }
            this.updateTypeIcon(e.costType[0]);
            const t = this.checkDailyBuyLimit(this.coinDefine, this.contentClick, false);
            this.checkisfree();
            if (0 == t) {
              this.goodcost.string = "已售罄";
              this.setDisable(this.contentClick.node);
            }
    }

    updateTypeIcon(e: any) {
        const t = this.typeicon.getChildByName("coin" + e);
            t && (t.active = true);
    }

    loadGoodItem(e: any) {
        if (this.ishigh) return;
            const t = this;
            const o = this.getGoodItemData(e);
            $2Manager.Manager.loader.loadPrefab("ui/ModeBackpackHero/goodItem").then(function (e) {
              e.setParent(t.goodnode);
              e.setPosition(0, 0);
              e.getComponent($2M20Gooditem.default).setdata(o);
            });
    }

    getGoodItemData(e: any) {
        const t = e.icon;
            const o = "";
            const i = false;
            const c = this.getCount(e);
            switch (e.type) {
              case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
              case $2CurrencyConfigCfg.CurrencyConfigDefine.Coin:
                t = "v1/images/icon/good" + e.id;
                break;
              case $2CurrencyConfigCfg.CurrencyConfigDefine.Box:
              case $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box:
                t = "v1/images/icon/good" + e.id.toString().slice(0, 3) + "01_shop";
                break;
              case $2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments:
                i = true;
                this.unlockCfg = $2Cfg.Cfg.RoleUnlock.find({
                  id: e.equipId
                });
                // o = "v1/images/bg/bg_icon_0" + this.unlockCfg.rarity;
                o = "v1/images/bg/bg_icon_0" + this.unlockCfg.rarity;
                this.unlockCfg && this.unlockCfg.icon && (t = this.unlockCfg.icon);
            }
            return {
              path: t,
              bgpath: o,
              isfrag: i,
              count: c
            };
    }

    checkisfree() {
        let e: any;
            let t: any;
            let o: any;
            let i: any;
            if (this.data.costType[0] == $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
              if (this.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin && $2Manager.Manager.vo.userVo.dailyData.freeCoin || this.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond && $2Manager.Manager.vo.userVo.dailyData.freeDiamond) {
                null === (e = this.typeicon) || e === undefined || e.setActive(false);
                this.goodcost.string = " 免 费 ";
              } else if (this.coinDefine == $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
                null === (t = this.typeicon) || t === undefined || t.setActive(true), null === (i = null === (o = this.typeicon) || o === undefined ? undefined : o.getChildByName("coin5")) || i === undefined || i.setActive(true), this.goodcost && (this.isNoAdLimit ? this.goodcost.string = " 免 费 " : this.curbuyCount < 0 ? this.goodcost.string = "已售罄" : this.goodcost.string = cc.js.formatStr("免费(%d)", this.curbuyCount));
              }
            }
    }

    adGet(e: any) {
        const t = this;
            const o = 100 == this.data.id ? "ShopAdGetBox" : "ShopAdGetAdvBox";
            this.sendEvent("click", o);
            $2Notifier.Notifier.send($2ListenID.ListenID.Ad_ShowVideo, function (i) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
              if (i == (window as any).wonderSdk.VideoAdCode.COMPLETE) {
                t.sendEvent("success", o);
                t.handleAdComplete(e);
              }
            });
            $2Time.Time.delay(2, function () {
              $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
            });
    }

    handleAdComplete(e: any) {
        const t = this;
            const o = this.data.costType.indexOf($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio);
            const i = this.data.id + "adcount";
            this.mode.dailyAdpack.addGoods(i, 1);
            const n = this.mode.dailyAdpack.getVal(i);
            if (n >= this.data.costVal[o]) {
              $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_GetBox", $2MVC.MVC.openArgs().setParam({
                data: this.data,
                cb: function () {
                  t.mode.fightinfopack.addGoods("boxcost", t.data.costVal[t.data.costType.indexOf($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond)] / 2);
                  $2Notifier.Notifier.send($2ListenID.ListenID.M20_UpdateBoxExp);
                }
              }));
              this.mode.dailyAdpack.useUp(i, n);
              this.coinDefine = $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio;
              0 == this.checkDailyBuyLimit($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio, e.currentTarget.getComponent(cc.Button)) && this.setDisable(e.currentTarget);
            }
    }

    sendEvent(e: any, t: any) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "reward_btn", Object.assign({
              Type: e,
              Scene: t,
              ADType: $2Notifier.Notifier.call($2CallID.CallID.Ad_VideoType)
            }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}));
    }

    checkDailyBuyLimit(e: any, t: any, o: any) {
        const i = this;
            o === undefined && (o = true);
            const n = this.mode.dailyAdpack.getVal(this.data.id + "adlimit");
            const r = this.data.dailyBuyCount[this.data.costType.indexOf(e)] || -1;
            if (e == $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
              switch (this.data.type) {
                case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
                case $2CurrencyConfigCfg.CurrencyConfigDefine.Coin:
                case $2CurrencyConfigCfg.CurrencyConfigDefine.Box:
                case $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box:
                  r = $2Manager.Manager.vo.switchVo.shopConfig.find(function (e) {
                    return e[0] == i.data.type;
                  })[1];
              }
              -1 == r && (this.isNoAdLimit = true);
            }
            if (-1 === r) {
              return -1;
            } else {
              return r && o && (this.mode.dailyAdpack.addGoods(this.data.id + "adlimit", 1), n++), t.interactable = r > n, this.updateDisableState(t.node, r <= n), this.curbuyCount = r - n, this.curbuyCount;
            }
    }

    updateDisableState(e: any, t: any) {
        const o = e.getChildByName("bgdisable");
            if (o) {
              o.active = t;
              const i = e.getChildByName("desc");
              i && (i.active = t);
              t && i && (this.cdStr = i.getComponent(cc.Label));
            }
    }

    costOnclick(e: any, t: any) {
        let o: any;
            const i = null !== (o = this.coinDefine) && undefined !== o ? o : this.data.costType[0];
            t && (i = Number(t));
            this.handleCostClick(i, e);
    }

    handleCostClick(e: any, t: any) {
        switch (e) {
              case $2CurrencyConfigCfg.CurrencyConfigDefine.Coin:
              case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
              case $2CurrencyConfigCfg.CurrencyConfigDefine.advboxkey:
                this.handleCurrencyCost(e, t);
                break;
              case $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio:
                this.handleVideoCost(e, t);
                break;
              case $2CurrencyConfigCfg.CurrencyConfigDefine.purchases:
                $2Manager.Manager.Shop.Buy($2Cfg.Cfg.PayShop.get(this.data.payShopId));
            }
    }

    handleCurrencyCost(e: any, t: any) {
        const o = this;
            const i = this.consList.find(function (t) {
              return t.type == +e;
            }).val * this.curdiscount;
            if ($2Manager.Manager.vo.knapsackVo.getVal(e) < i) {
              $2AlertManager.AlertManager.showNormalTips($2Cfg.Cfg.CurrencyConfig.get(Number(e)).name + "不足");
            } else {
              const n = function () {
                o.getReward(e);
                if (0 == o.checkDailyBuyLimit(e, t.currentTarget.getComponent(cc.Button))) {
                  o.goodcost.string = "已售罄";
                  o.setDisable(t.currentTarget);
                }
                $2Manager.Manager.vo.knapsackVo.useUp(e, Math.floor(i));
              };
              if (this.mode.fragments.includes(this.data.type)) {
                $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_ShopBuyConfirm", $2MVC.MVC.openArgs().setParam({
                  cb: n,
                  cfg: this.data,
                  disCount: this.curdiscount
                }));
              } else {
                n();
              }
            }
    }

    handleVideoCost(e: any, t: any) {
        const o = this;
            if (this.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin && $2Manager.Manager.vo.userVo.dailyData.freeCoin || this.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond && $2Manager.Manager.vo.userVo.dailyData.freeDiamond) {
              if (this.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin) {
                $2Manager.Manager.vo.userVo.dailyData.freeCoin = false;
              } else {
                $2Manager.Manager.vo.userVo.dailyData.freeDiamond = false;
              }
              $2Manager.Manager.vo.saveUserData();
              return void this.getReward();
            }
            const i = this.getCount(this.data);
            $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_ShopBuyConfirm", $2MVC.MVC.openArgs().setParam({
              cb: function () {
                const e = o.data.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond ? "ShopAdGetDiamond" : "ShopAdGetCoin";
                o.sendEvent("click", e);
                $2Notifier.Notifier.send($2ListenID.ListenID.Ad_ShowVideo, function (n) {
                  if (n == (window as any).wonderSdk.VideoAdCode.COMPLETE) {
                    o.sendEvent("success", e);
                    const r = o.data.type;
                    if (3e4 == o.data.id) {
                      r = $2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out;
                      i = $2GameUtil.GameUtil.getRandomByWeightInArray($2Manager.Manager.vo.switchVo.shopAdGift, 1)[0];
                    }
                    const a = o.checkDailyBuyLimit($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio, t.currentTarget.getComponent(cc.Button));
                    const s = $2Cfg.Cfg.CurrencyConfig.get(r);
                    $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, [{
                      id: r,
                      num: i,
                      type: s.type,
                      rarity: s.rarity
                    }]);
                    if (0 == a) {
                      o.goodcost.string = "已售罄";
                      o.setDisable(t.currentTarget);
                    }
                  }
                });
                $2Time.Time.delay(2, function () {
                  $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
                });
              },
              cfg: this.data,
              count: i,
              disCount: this.curdiscount
            }));
    }

    getCount(e: any) {
        if (e.costType[0] === $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio && (e.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin || e.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond)) {
              const t = this.mode.dailyAdpack.getVal(this.data.id + "adlimit");
              const o = e.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin ? 50 : 20;
              return e.getNum + t * o;
            }
            return e.getNum;
    }

    update() {
        if (this.cdStr) {
              const e = $2GameUtil.GameUtil.formatSeconds($2GameUtil.GameUtil.secondsUntilNextDay());
              this.cdStr.string = e.str;
            }
    }

    getReward(e: any) {
        const t = this;
            e === undefined && (e = $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio);
            const o = false;
            switch (this.data.type) {
              case $2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments:
                $2Manager.Manager.audio.playAudio(2001);
                break;
              case $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box:
              case $2CurrencyConfigCfg.CurrencyConfigDefine.Box:
                $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_GetBox", $2MVC.MVC.openArgs().setParam({
                  data: this.data,
                  cb: function () {
                    if (e == $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond) {
                      t.mode.fightinfopack.addGoods("boxcost", t.data.costVal[t.data.costType.indexOf($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond)]);
                      $2Notifier.Notifier.send($2ListenID.ListenID.M20_UpdateBoxExp);
                    }
                  }
                }));
                o = true;
                $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 11);
            }
            this.checkisfree();
            if (!o) {
              const i = [1, 2, 11].includes(this.data.type);
              const n = [{
                id: i ? this.data.type : this.data.equipId,
                num: this.data.getNum,
                type: i ? $2GameSeting.GameSeting.GoodsType.Money : $2GameSeting.GameSeting.GoodsType.Fragment
              }];
              $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, n);
            }
    }

    setDisable(e: any) {
        e.getChildByName("bgdisable").active = true;
    }

    onEnable() {
        this.changeListener(true);
    }

    onDisable() {
        this.changeListener(false);
    }

    changeListener(e: any) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Refresh_Item, this.onRefresh_Item, this);
    }

    onRefresh_Item() {
        this.resetConst();
            this.updateCostUI(this.data, this.ishigh);
    }

}
