import $2CallID from "./$2CallID";
import $2GameSeting from "./$2GameSeting";
import $2ListenID from "./$2ListenID";
import $2Cfg from "./$2Cfg";
import $2CurrencyConfigCfg from "./$2CurrencyConfigCfg";
import $2MVC from "./$2MVC";
import $2Notifier from "./$2Notifier";
import $2NotifyID from "./$2NotifyID";
import $2Manager from "./$2Manager";
import $2Time from "./$2Time";
import $2UIManager from "./$2UIManager";
import $2GameUtil from "./$2GameUtil";
import $2AlertManager from "./$2AlertManager";
import $2RBadgeModel from "./$2RBadgeModel";
import $2ModeBackpackHeroModel from "./$2ModeBackpackHeroModel";
import $2M20Gooditem from "./$2M20Gooditem";

const { ccclass, property } = cc._decorator;

interface CostData {
    type: number;
    val: number;
}

interface GoodItemData {
    path: string;
    bgpath: string;
    isfrag: boolean;
    count: number;
}

@ccclass
export default class M20_PartItem extends cc.Component {
    @property(cc.Label)
    goodname: cc.Label = null;

    @property(cc.Sprite)
    bg: cc.Sprite = null;

    @property(cc.Label)
    goodcost: cc.Label = null;

    @property(cc.RichText)
    gooddesc: cc.RichText = null;

    @property(cc.Node)
    goodnode: cc.Node = null;

    @property(cc.Node)
    rewardnode: cc.Node = null;

    @property(cc.Node)
    typeicon: cc.Node = null;

    @property(cc.Node)
    discountnode: cc.Node = null;

    @property(cc.RichText)
    adUnlockCount: cc.RichText = null;

    @property(cc.Label)
    coinUnlockCount: cc.Label = null;

    @property(cc.Button)
    contentClick: cc.Button = null;

    @property(cc.Button)
    adClick: cc.Button = null;

    cdStr: cc.Label = null;
    curbuyCount: number = 0;
    curdiscount: number = 1;
    isNoAdLimit: boolean = false;
    consList: CostData[] = [];
    data: any;
    ishigh: boolean;
    coinDefine: number;
    unlockCfg: any;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    setdata(data: any, ishigh: boolean, discount: number) {
        this.data = data;
        this.curdiscount = discount;
        this.ishigh = ishigh;
        this.resetConst();
        this.updateUI(data, ishigh, discount);

        if (data.id === 200) {
            $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Add, this.node, $2RBadgeModel.RBadge.Key.Shop_FreeCoin);
        } else if (data.id === 300) {
            $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Add, this.node, $2RBadgeModel.RBadge.Key.Shop_FreeDiamond);
        } else {
            $2Notifier.Notifier.send($2ListenID.ListenID.Badge_Add, this.node, null);
        }
    }

    resetConst() {
        this.consList.length = 0;
        this.data.costType.forEach((type: number, index: number) => {
            this.consList.push(this.getConst(type, this.data.costVal[index]));
        });
    }

    showreward() {
        const targetScale = this.rewardnode.scaleY === 0 ? 1 : 0;
        const targetOpacity = targetScale === 1 ? 255 : 0;
        
        cc.tween(this.rewardnode)
            .to(0.1, { scaleY: targetScale, opacity: targetOpacity })
            .start();

        const listNode = this.rewardnode.children[0].children[1];
        const templateNode = this.rewardnode.children[0].children[1].children[0];

        this.data.boxReward.forEach((reward: any[], index: number) => {
            const count = reward[1];
            const id = reward[0];
            const itemNode = listNode.children[index] || cc.instantiate(templateNode);
            itemNode.setParent(listNode);
            itemNode.active = true;

            const currencyConfig = $2Cfg.Cfg.CurrencyConfig.find({ id: id });
            let iconPath: string;
            
            if (this.mode.fragments.includes(id)) {
                iconPath = $2GameSeting.GameSeting.getRarity(this.mode.buffmap[currencyConfig.id]).framgimg;
            } else {
                iconPath = currencyConfig.icon;
            }

            $2Manager.Manager.loader.loadSpriteToSprit(iconPath, itemNode.getChildByName("icon").getComponent(cc.Sprite), this.node);
            itemNode.getChildByName("num").getComponent(cc.Label).string = "x" + count;
        });
    }

    updateUI(data: any, ishigh: boolean, discount: number) {
        this.goodname.string = data.title;
        this.resetUI();
        this.updateDiscountUI(discount);
        this.updateDescription(data);
        this.updateCostUI(data, ishigh);
        // if (data.bgpath) {
        //     $2Manager.Manager.loader.loadSpriteToSprit(data.bgpath, this.bg, this.node);
        // }
        this.loadGoodItem(data);
    }

    resetUI() {
        this.adClick?.node.getChildByName("bgdisable")?.setActive(false);
        this.contentClick?.node.getChildByName("bgdisable")?.setActive(false);
        this.typeicon?.children.forEach(child => child.setActive(false));
    }

    updateDiscountUI(discount: number) {
        if (this.discountnode) {
            this.discountnode.active = discount !== 1;
            this.discountnode.getComponentInChildren(cc.Label).string = cc.js.formatStr("%d折", 10 * discount);
        }
    }

    updateDescription(data: any) {
        if (data.costType[0] !== $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio || 
            (data.type !== $2CurrencyConfigCfg.CurrencyConfigDefine.Coin && data.type !== $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond)) {
            this.gooddesc.text = data.desc !== "" ? 
                cc.js.formatStr(data.desc) : 
                cc.js.formatStr("<color=#474747>x%d</color>", this.getCount(data));
        }
        // else {
        //     this.gooddesc.text = cc.js.formatStr("<color=#474747>x%d</color>", this.getCount(data));
        // }
    }

    updateCostUI(data: any, ishigh: boolean) {
        if (ishigh) {
            this.handleHighLevelBox(data);
        } else {
            this.handleNormalItem(data);
        }
    }

    getConst(type: number, val: number): CostData {
        const cost: CostData = { type: type, val: val };
        
        switch (cost.type) {
            case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
                if ($2Manager.Manager.vo.knapsackVo.has($2CurrencyConfigCfg.CurrencyConfigDefine.advboxkey) && 
                    this.data.id >= 10101 && this.data.id <= 10110) {
                    cost.type = $2CurrencyConfigCfg.CurrencyConfigDefine.advboxkey;
                    cost.val = 1;
                }
                break;
        }
        
        return cost;
    }

    handleHighLevelBox(data: any) {
        let adDisabled = false;
        let coinDisabled = false;

        data.costType.forEach((type: number, index: number) => {
            if (type === $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
                adDisabled = this.checkDailyBuyLimit($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio, this.adClick, false) === 0;
                const descAds = this.adClick.node.getChildByName("desc_ads");
                
                if (adDisabled) {
                    descAds.active = false;
                } else {
                    descAds.active = true;
                    if (this.isNoAdLimit) {
                        descAds.getComponent(cc.Label).string = " 免 费 ";
                    } else {
                        descAds.getComponent(cc.Label).string = cc.js.formatStr("免费(%d)", this.curbuyCount);
                    }
                }
            } else if (type === $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond) {
                this.coinUnlockCount.string = this.consList[index].val.toString();
                this.coinDefine = data.costType[index];
                coinDisabled = this.checkDailyBuyLimit(this.coinDefine, this.adClick, false) === 0;
                
                $2Manager.Manager.loader.loadSpriteToSprit(
                    $2Cfg.Cfg.CurrencyConfig.get(this.consList[index].type).icon,
                    this.coinUnlockCount.node.parent.getComByPath(cc.Sprite, "icon_ads"),
                    this.node
                );
                
                this.coinUnlockCount.node.parent.parent.getComponent(cc.Button).clickEvents[0].customEventData = this.consList[index].type.toString();
            }
        });

        if (adDisabled && coinDisabled) {
            this.setDisable(this.adClick.node);
        }
    }

    handleNormalItem(data: any) {
        this.coinDefine = data.costType[0];
        
        if (this.coinDefine === $2CurrencyConfigCfg.CurrencyConfigDefine.purchases) {
            this.goodcost.string = $2Manager.Manager.Shop.getFromPrice($2Cfg.Cfg.PayShop.get(data.payShopId));
        } else {
            this.goodcost.string = Math.floor(data.costVal[0] * this.curdiscount).toString();
        }
        
        this.updateTypeIcon(data.costType[0]);
        const remainingCount = this.checkDailyBuyLimit(this.coinDefine, this.contentClick, false);
        this.checkisfree();
        
        if (remainingCount === 0) {
            this.goodcost.string = "已售罄";
            this.setDisable(this.contentClick.node);
        }
    }

    updateTypeIcon(costType: number) {
        const typeIcon = this.typeicon.getChildByName("coin" + costType);
        if (typeIcon) {
            typeIcon.active = true;
        }
    }

    loadGoodItem(data: any) {
        if (this.ishigh) return;
        
        const goodItemData = this.getGoodItemData(data);
        $2Manager.Manager.loader.loadPrefab("ui/ModeBackpackHero/goodItem").then((prefab: cc.Node) => {
            prefab.setParent(this.goodnode);
            prefab.setPosition(0, 0);
            prefab.getComponent($2M20Gooditem.default).setdata(goodItemData);
        });
    }

    getGoodItemData(data: any): GoodItemData {
        let iconPath = data.icon;
        let bgPath = "";
        let isFragment = false;
        const count = this.getCount(data);

        switch (data.type) {
            case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
            case $2CurrencyConfigCfg.CurrencyConfigDefine.Coin:
                iconPath = "v1/images/icon/good" + data.id;
                break;
            case $2CurrencyConfigCfg.CurrencyConfigDefine.Box:
            case $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box:
                iconPath = "v1/images/icon/good" + data.id.toString().slice(0, 3) + "01_shop";
                break;
            case $2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments:
                isFragment = true;
                this.unlockCfg = $2Cfg.Cfg.RoleUnlock.find({ id: data.equipId });
                bgPath = "v1/images/bg/bg_icon_0" + this.unlockCfg.rarity;
                if (this.unlockCfg && this.unlockCfg.icon) {
                    iconPath = this.unlockCfg.icon;
                }
                break;
        }

        return {
            path: iconPath,
            bgpath: bgPath,
            isfrag: isFragment,
            count: count
        };
    }

    checkisfree() {
        if (this.data.costType[0] === $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
            if ((this.data.type === $2CurrencyConfigCfg.CurrencyConfigDefine.Coin && $2Manager.Manager.vo.userVo.dailyData.freeCoin) ||
                (this.data.type === $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond && $2Manager.Manager.vo.userVo.dailyData.freeDiamond)) {
                this.typeicon?.setActive(false);
                this.goodcost.string = " 免 费 ";
            } else if (this.coinDefine === $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
                this.typeicon?.setActive(true);
                this.typeicon?.getChildByName("coin5")?.setActive(true);

                if (this.goodcost) {
                    if (this.isNoAdLimit) {
                        this.goodcost.string = " 免 费 ";
                    } else if (this.curbuyCount < 0) {
                        this.goodcost.string = "已售罄";
                    } else {
                        this.goodcost.string = cc.js.formatStr("免费(%d)", this.curbuyCount);
                    }
                }
            }
        }
    }

    adGet(event: any) {
        const eventName = this.data.id === 100 ? "ShopAdGetBox" : "ShopAdGetAdvBox";
        this.sendEvent("click", eventName);

        $2Notifier.Notifier.send($2ListenID.ListenID.Ad_ShowVideo, (result: any) => {
            $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
            if (result === (window as any).wonderSdk.VideoAdCode.COMPLETE) {
                this.sendEvent("success", eventName);
                this.handleAdComplete(event);
            }
        });

        $2Time.Time.delay(2, () => {
            $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
        });
    }

    handleAdComplete(event: any) {
        const videoIndex = this.data.costType.indexOf($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio);
        const adCountKey = this.data.id + "adcount";
        this.mode.dailyAdpack.addGoods(adCountKey, 1);
        const currentCount = this.mode.dailyAdpack.getVal(adCountKey);

        if (currentCount >= this.data.costVal[videoIndex]) {
            $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_GetBox", $2MVC.MVC.openArgs().setParam({
                data: this.data,
                cb: () => {
                    this.mode.fightinfopack.addGoods("boxcost", this.data.costVal[this.data.costType.indexOf($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond)] / 2);
                    $2Notifier.Notifier.send($2ListenID.ListenID.M20_UpdateBoxExp);
                }
            }));

            this.mode.dailyAdpack.useUp(adCountKey, currentCount);
            this.coinDefine = $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio;

            if (this.checkDailyBuyLimit($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio, event.currentTarget.getComponent(cc.Button)) === 0) {
                this.setDisable(event.currentTarget);
            }
        }
    }

    sendEvent(type: string, scene: string) {
        $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "reward_btn", Object.assign({
            Type: type,
            Scene: scene,
            ADType: $2Notifier.Notifier.call($2CallID.CallID.Ad_VideoType)
        }, $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutGameData) || {}));
    }

    checkDailyBuyLimit(costType: number, button: cc.Button, shouldUpdate: boolean = true): number {
        const currentUsed = this.mode.dailyAdpack.getVal(this.data.id + "adlimit");
        let dailyLimit = this.data.dailyBuyCount[this.data.costType.indexOf(costType)] || -1;

        if (costType === $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
            switch (this.data.type) {
                case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
                case $2CurrencyConfigCfg.CurrencyConfigDefine.Coin:
                case $2CurrencyConfigCfg.CurrencyConfigDefine.Box:
                case $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box:
                    dailyLimit = $2Manager.Manager.vo.switchVo.shopConfig.find((config: any[]) => config[0] === this.data.type)[1];
                    break;
            }

            if (dailyLimit === -1) {
                this.isNoAdLimit = true;
            }
        }

        if (dailyLimit === -1) {
            return -1;
        } else {
            if (dailyLimit && shouldUpdate) {
                this.mode.dailyAdpack.addGoods(this.data.id + "adlimit", 1);
                currentUsed++;
            }

            button.interactable = dailyLimit > currentUsed;
            this.updateDisableState(button.node, dailyLimit <= currentUsed);
            this.curbuyCount = dailyLimit - currentUsed;
            return this.curbuyCount;
        }
    }

    updateDisableState(node: cc.Node, isDisabled: boolean) {
        const disableBg = node.getChildByName("bgdisable");
        if (disableBg) {
            disableBg.active = isDisabled;
            const descNode = node.getChildByName("desc");
            if (descNode) {
                descNode.active = isDisabled;
                if (isDisabled) {
                    this.cdStr = descNode.getComponent(cc.Label);
                }
            }
        }
    }

    costOnclick(event: any, customEventData?: string) {
        let costType = this.coinDefine ?? this.data.costType[0];
        if (customEventData) {
            costType = Number(customEventData);
        }
        this.handleCostClick(costType, event);
    }

    handleCostClick(costType: number, event: any) {
        switch (costType) {
            case $2CurrencyConfigCfg.CurrencyConfigDefine.Coin:
            case $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond:
            case $2CurrencyConfigCfg.CurrencyConfigDefine.advboxkey:
                this.handleCurrencyCost(costType, event);
                break;
            case $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio:
                this.handleVideoCost(costType, event);
                break;
            case $2CurrencyConfigCfg.CurrencyConfigDefine.purchases:
                $2Manager.Manager.Shop.Buy($2Cfg.Cfg.PayShop.get(this.data.payShopId));
                break;
        }
    }

    handleCurrencyCost(costType: number, event: any) {
        const cost = this.consList.find(item => item.type === +costType).val * this.curdiscount;

        if ($2Manager.Manager.vo.knapsackVo.getVal(costType) < cost) {
            $2AlertManager.AlertManager.showNormalTips($2Cfg.Cfg.CurrencyConfig.get(Number(costType)).name + "不足");
        } else {
            const buyAction = () => {
                this.getReward(costType);
                if (this.checkDailyBuyLimit(costType, event.currentTarget.getComponent(cc.Button)) === 0) {
                    this.goodcost.string = "已售罄";
                    this.setDisable(event.currentTarget);
                }
                $2Manager.Manager.vo.knapsackVo.useUp(costType, Math.floor(cost));
            };

            if (this.mode.fragments.includes(this.data.type)) {
                $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_ShopBuyConfirm", $2MVC.MVC.openArgs().setParam({
                    cb: buyAction,
                    cfg: this.data,
                    disCount: this.curdiscount
                }));
            } else {
                buyAction();
            }
        }
    }

    handleVideoCost(costType: number, event: any) {
        if ((this.data.type === $2CurrencyConfigCfg.CurrencyConfigDefine.Coin && $2Manager.Manager.vo.userVo.dailyData.freeCoin) ||
            (this.data.type === $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond && $2Manager.Manager.vo.userVo.dailyData.freeDiamond)) {

            if (this.data.type === $2CurrencyConfigCfg.CurrencyConfigDefine.Coin) {
                $2Manager.Manager.vo.userVo.dailyData.freeCoin = false;
            } else {
                $2Manager.Manager.vo.userVo.dailyData.freeDiamond = false;
            }

            $2Manager.Manager.vo.saveUserData();
            this.getReward();
            return;
        }

        const count = this.getCount(this.data);
        $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_ShopBuyConfirm", $2MVC.MVC.openArgs().setParam({
            cb: () => {
                const eventName = this.data.type === $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond ? "ShopAdGetDiamond" : "ShopAdGetCoin";
                this.sendEvent("click", eventName);

                $2Notifier.Notifier.send($2ListenID.ListenID.Ad_ShowVideo, (result: any) => {
                    if (result === (window as any).wonderSdk.VideoAdCode.COMPLETE) {
                        this.sendEvent("success", eventName);
                        let rewardType = this.data.type;
                        let rewardCount = count;

                        if (this.data.id === 30000) {
                            rewardType = $2CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out;
                            rewardCount = $2GameUtil.GameUtil.getRandomByWeightInArray($2Manager.Manager.vo.switchVo.shopAdGift, 1)[0];
                        }

                        const remainingCount = this.checkDailyBuyLimit($2CurrencyConfigCfg.CurrencyConfigDefine.Vedio, event.currentTarget.getComponent(cc.Button));
                        const currencyConfig = $2Cfg.Cfg.CurrencyConfig.get(rewardType);

                        $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, [{
                            id: rewardType,
                            num: rewardCount,
                            type: currencyConfig.type,
                            rarity: currencyConfig.rarity
                        }]);

                        if (remainingCount === 0) {
                            this.goodcost.string = "已售罄";
                            this.setDisable(event.currentTarget);
                        }
                    }
                });

                $2Time.Time.delay(2, () => {
                    $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
                });
            },
            cfg: this.data,
            count: count,
            disCount: this.curdiscount
        }));
    }

    getCount(data: any): number {
        if (data.costType[0] === $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio &&
            (data.type === $2CurrencyConfigCfg.CurrencyConfigDefine.Coin || data.type === $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond)) {
            const usedCount = this.mode.dailyAdpack.getVal(this.data.id + "adlimit");
            const bonus = data.type === $2CurrencyConfigCfg.CurrencyConfigDefine.Coin ? 50 : 20;
            return data.getNum + usedCount * bonus;
        }
        return data.getNum;
    }

    update() {
        if (this.cdStr) {
            const timeData = $2GameUtil.GameUtil.formatSeconds($2GameUtil.GameUtil.secondsUntilNextDay());
            this.cdStr.string = timeData.str;
        }
    }

    getReward(costType: number = $2CurrencyConfigCfg.CurrencyConfigDefine.Vedio) {
        let shouldShowRewardPopup = false;

        switch (this.data.type) {
            case $2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments:
                $2Manager.Manager.audio.playAudio(2001);
                break;
            case $2CurrencyConfigCfg.CurrencyConfigDefine.High_Box:
            case $2CurrencyConfigCfg.CurrencyConfigDefine.Box:
                $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_GetBox", $2MVC.MVC.openArgs().setParam({
                    data: this.data,
                    cb: () => {
                        if (costType === $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond) {
                            this.mode.fightinfopack.addGoods("boxcost", this.data.costVal[this.data.costType.indexOf($2CurrencyConfigCfg.CurrencyConfigDefine.Diamond)]);
                            $2Notifier.Notifier.send($2ListenID.ListenID.M20_UpdateBoxExp);
                        }
                    }
                }));
                shouldShowRewardPopup = true;
                $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 11);
                break;
        }

        this.checkisfree();

        if (!shouldShowRewardPopup) {
            const isMoneyType = [1, 2, 11].includes(this.data.type);
            const rewards = [{
                id: isMoneyType ? this.data.type : this.data.equipId,
                num: this.data.getNum,
                type: isMoneyType ? $2GameSeting.GameSeting.GoodsType.Money : $2GameSeting.GameSeting.GoodsType.Fragment
            }];
            $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, rewards);
        }
    }

    setDisable(node: cc.Node) {
        node.getChildByName("bgdisable").active = true;
    }

    onDestroy() {}

    start() {}

    onEnable() {
        this.changeListener(true);
    }

    onDisable() {
        this.changeListener(false);
    }

    changeListener(isAdd: boolean) {
        $2Notifier.Notifier.changeListener(isAdd, $2ListenID.ListenID.Refresh_Item, this.onRefresh_Item, this);
    }

    onRefresh_Item() {
        this.resetConst();
        this.updateCostUI(this.data, this.ishigh);
    }
}
