import $2Cfg from "./Cfg";
import $2SoundCfg from "./SoundCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2NotifyID from "./NotifyID";
import $2GameSeting from "./GameSeting";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";
import $2PropertyVo from "./PropertyVo";
import $2MBackpackHero from "./MBackpackHero";
import $2M20Prop from "./M20Prop";
import $2M20Prop_Equip from "./M20Prop_Equip";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20Prop_Gemstone extends $2M20Prop.default {
    get saveData() {
        return {
        id: this.mergeCfg.id,
        pos: this.position,
        curHp: this.curHp,
        isUnlock: this.isUnlock,
        gemData: this.gemData
      };
    }

    excute() {
        if (this.roleCfg.id == $2MBackpackHero.MBPack.ReactionType.MagicallyChange) {
              this.setReaction({
                type: $2MBackpackHero.MBPack.ReactionType.MagicallyChange,
                item: this,
                prem: $2GameUtil.GameUtil.getRandomByWeightInList(this.mergeCfg.gemWeight)[0].id
              });
            } else if (this.roleID == $2MBackpackHero.MBPack.ReactionType.GeGeJi) {
              this.gemData.eggOpenNum = 1, this.showTips("<outline color=black width=2>点击下蛋</outline>", true);
            }
    }

    onTouchEnd(t: any) {
        super.onTouchEnd(t);
            if (this._frameNum < 6 && this.isUnlock) {
              if (this.gemData.eggOpenNum > 0) {
                this.setReaction({
                  type: $2MBackpackHero.MBPack.ReactionType.GeGeJi,
                  item: this
                });
              } else {
                $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_GemInfo", $2MVC.MVC.openArgs().setParam(this));
              }
            }
    }

    set(t: any, o: any) {
        const i = this;
            super.set(t, o);
            this.resetState(cc.rect($2MBackpackHero.MBPack.BlockSize, $2MBackpackHero.MBPack.BlockSize));
            this.gemData = {
              id: this.roleID,
              mergeID: t.id,
              MagicallyChangeID: 0,
              GreedyGemstoneNum: 0,
              SacrificeNum: 5,
              eggOpenNum: 0
            };
            this.setProperty();
            this.initHp();
            this.node.getChildByName("gemStones").setActive(false);
            this.lineBox = this.node.getORaddChildByName("line");
            this.resetGemState();
            this.scheduleOnce(function () {
              i.showTips();
            }, .5);
    }

    resetGemState() {
        this.node.getComByChild(cc.Label, "name").setAttribute({
              string: this.gemCfg.name
            }).node.setActive(true);
            this.node.getComByChild(cc.Label, "num").setAttribute({
              string: this.gemData.SacrificeNum
            }).node.setActive(this.roleID == $2MBackpackHero.MBPack.ReactionType.Sacrifice);
    }

    setProperty() {
        this.property || (this.property = new $2PropertyVo.Property.Vo(this));
    }

    checkReaction(e: any, t: any) {
        let i: any;
            t === undefined && (t = false);
            if (e && this != e && e.isUnlock) {
              if (e instanceof $2M20Prop_Equip.default) {
                if (this.roleID == $2MBackpackHero.MBPack.ReactionType.CopyEquip) {
                  return {
                    type: this.roleID,
                    item: e,
                    d: 0
                  };
                }
                if (this.roleID == $2MBackpackHero.MBPack.ReactionType.MinEquip) {
                  if (e.blockType <= 1) {
                    t && $2AlertManager.AlertManager.showNormalTips("1格装备无法缩小");
                  } else {
                    if (this.gemCfg.relateEquip.includes(e.mergeCfg.id)) {
                      return {
                        type: this.roleID,
                        item: e,
                        d: 0
                      };
                    }
                    t && $2AlertManager.AlertManager.showNormalTips("只能缩小4级进阶装备");
                  }
                } else if (this.roleID == $2MBackpackHero.MBPack.ReactionType.DestinyGem) {
                  if (this.gemCfg.relateEquip.includes(e.mergeCfg.id)) {
                    return {
                      type: this.roleID,
                      item: e,
                      d: 0
                    };
                  }
                  t && $2AlertManager.AlertManager.showNormalTips("只能升级装备");
                } else if (this.roleID == $2MBackpackHero.MBPack.ReactionType.MOONCAKE) {
                  if (3 != e.mergeCfg.lv || e.property.cut.atk <= 0) {
                    t && $2AlertManager.AlertManager.showNormalTips("只能镶嵌3级武器");
                  } else {
                    if (![5020].includes(e.mergeCfg.id)) {
                      return {
                        type: this.roleID,
                        item: e,
                        d: 0
                      };
                    }
                    t && $2AlertManager.AlertManager.showNormalTips("无法复制");
                  }
                } else if (null === (i = this.gemCfg.relateEquip) || i === undefined ? undefined : i.includes(e.mergeCfg.id)) {
                  return {
                    type: $2MBackpackHero.MBPack.ReactionType.Mosaic,
                    item: e,
                    d: 0
                  };
                }
                if (t) {
                  this.roleID == $2MBackpackHero.MBPack.ReactionType.CopyGem && $2AlertManager.AlertManager.showNormalTips("只能克隆其他宝石");
                  this.roleID == $2MBackpackHero.MBPack.ReactionType.Sacrifice && $2AlertManager.AlertManager.showNormalTips("无法镶嵌,点击宝石看详情");
                }
              } else if (e instanceof o && this.roleID == $2MBackpackHero.MBPack.ReactionType.CopyGem) {
                return {
                  type: $2MBackpackHero.MBPack.ReactionType.CopyGem,
                  item: e,
                  d: 0
                };
              }
              return null;
            }
    }

    setReaction(e: any) {
        let t: any;
            const o = this;
            const i = e.item;
            switch (e.type) {
              case $2MBackpackHero.MBPack.ReactionType.Mosaic:
                if (i instanceof $2M20Prop_Equip.default) {
                  if (i.isGemMax) {
                    this.packView.setInSpareBox(this);
                    return $2AlertManager.AlertManager.showNormalTips("镶嵌满了");
                  }
                  if (!this.gemCfg.relateEquip.includes(i.mergeCfg.id)) {
                    if ($2MBackpackHero.MBPack.equipWeapon.includes(this.gemCfg.relateEquip[0])) {
                      this.packView.setInSpareBox(this);
                      return $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("只能镶嵌在%s上", "武器"));
                    }
                    if ($2MBackpackHero.MBPack.equipArmor.includes(this.gemCfg.relateEquip[0])) {
                      this.packView.setInSpareBox(this);
                      return $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("只能镶嵌在%s上", "防具"));
                    }
                  }
                  if (this.roleID == $2MBackpackHero.MBPack.ReactionType.GreedyGemstone) {
                    if (i.gemStones.find(function (e) {
                      return e.roleID == $2MBackpackHero.MBPack.ReactionType.GreedyGemstone;
                    })) {
                      this.packView.setInSpareBox(this);
                      return $2AlertManager.AlertManager.showNormalTips("当前装备已镶嵌贪婪宝石");
                    }
                    if (!this.gemCfg.relateEquip.includes(i.mergeCfg.id)) {
                      this.packView.setInSpareBox(this);
                      return $2AlertManager.AlertManager.showNormalTips("贪婪宝石只能镶嵌在4级进阶装备");
                    }
                    i.setMosaic(this);
                    $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Fight_Turntable", $2MVC.MVC.openArgs().setParam(i));
                  } else if (this.mergeCfg.id == $2MBackpackHero.MBPack.ReactionType.MagicallyChange) {
                    this.packView.newProp(this.gemCfg, {
                      position: i.position
                    }).then(function (e) {
                      i.setMosaic(e);
                    });
                    this.unuse();
                  } else {
                    i.setMosaic(this);
                  }
                  this.showMosaicEffect(i);
                  this.changeToch(false);
                  this.node.setActive(false);
                }
                break;
              case $2MBackpackHero.MBPack.ReactionType.DestinyGem:
                if (i.isMaxLv) {
                  this.packView.setInSpareBox(this);
                  return $2AlertManager.AlertManager.showNormalTips("该装备已满级");
                }
                $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
                i.node.setActive(false);
                t = this.getWeight(i);
                $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs5_activation", {
                  nodeAttr: {
                    parent: this.packView.topEffectNode,
                    position: this.position
                  },
                  spAttr: {
                    animation: "animation" + (t ? 2 : 1),
                    type: $2GameSeting.GameSeting.TweenType.Not,
                    loop: false
                  },
                  delayRemove: 1
                }, this.game.gameNode);
                this.packView.scheduleOnce(function () {
                  if (t) {
                    i.node.setActive(true);
                    i.upgrade();
                    !i.isMaxLv && o.packView.scheduleOnce(function () {
                      i.upgrade();
                    }, .3);
                  } else {
                    $2AlertManager.AlertManager.showNormalTips("提升失败");
                    i.unuse();
                  }
                }, .8);
                this.unuse();
                break;
              case $2MBackpackHero.MBPack.ReactionType.CopyEquip:
                $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
                t = this.getWeight(i);
                $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs3_activation", {
                  delayRemove: 1,
                  nodeAttr: {
                    parent: this.packView.topEffectNode,
                    position: this.position
                  },
                  spAttr: {
                    animation: "animation" + (t ? 2 : 1),
                    type: $2GameSeting.GameSeting.TweenType.Not,
                    loop: false
                  }
                }, this.game.gameNode);
                this.packView.scheduleOnce(function () {
                  if (t) {
                    o.packView.newProp(i.mergeCfg, {
                      position: i.position
                    }).then(function (e) {
                      let t: any;
                      o.packView.setInSpareBox(e);
                      i.isUnlock || e.setVideoLock();
                      null === (t = e.buffMgr) || t === undefined || t.add(5e4);
                      $2AlertManager.AlertManager.showNormalTips("复制成功");
                    });
                  } else {
                    $2AlertManager.AlertManager.showNormalTips("复制失败");
                  }
                }, 1);
                this.unuse();
                break;
              case $2MBackpackHero.MBPack.ReactionType.CopyGem:
                if (!this.gemCfg.relateEquip.includes(i.mergeCfg.id)) {
                  this.packView.setInSpareBox(this);
                  return $2AlertManager.AlertManager.showNormalTips("无法克隆该宝石");
                }
                $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
                t = this.getWeight(i);
                $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs2_activation", {
                  delayRemove: 1,
                  nodeAttr: {
                    parent: this.packView.topEffectNode,
                    position: this.position
                  },
                  spAttr: {
                    animation: "animation" + (t ? 2 : 1),
                    type: $2GameSeting.GameSeting.TweenType.Not,
                    loop: false
                  }
                }, this.game.gameNode);
                this.packView.scheduleOnce(function () {
                  t && o.packView.newProp(i.gemCfg, {
                    position: i.position
                  }).then(function (e) {
                    o.packView.setInSpareBox(e);
                    e.setUnlock();
                  });
                  $2AlertManager.AlertManager.showNormalTips(t ? "克隆成功" : "克隆失败");
                }, 1);
                this.unuse();
                break;
              case $2MBackpackHero.MBPack.ReactionType.MOONCAKE:
                if (i instanceof $2M20Prop_Equip.default) {
                  t = this.getWeight(i);
                  $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs20_activation", {
                    delayRemove: 1,
                    nodeAttr: {
                      parent: this.packView.topEffectNode,
                      position: this.position
                    },
                    spAttr: {
                      animation: "animation" + (t ? 2 : 1),
                      type: $2GameSeting.GameSeting.TweenType.Not,
                      loop: false
                    }
                  }, this.game.gameNode);
                  this.packView.scheduleOnce(function () {
                    if (t) {
                      o.packView.newProp($2Cfg.Cfg.EquipMergeLv.get(5020), {
                        position: i.position
                      }).then(function (e) {
                        o.packView.setInSpareBox(e);
                        i.skillMgr.clearAll();
                        e.cloneBy(i);
                        $2AlertManager.AlertManager.showNormalTips("成功");
                        i.unuse();
                      });
                    } else {
                      $2AlertManager.AlertManager.showNormalTips("失败");
                    }
                    o.unuse();
                  }, 1);
                }
                break;
              case $2MBackpackHero.MBPack.ReactionType.Sacrifice:
                if (i instanceof $2M20Prop_Equip.default) {
                  const n = this.gemCfg.gemWeight.filter(function (e) {
                    return e[2] == i.mergeCfg.lv;
                  });
                  if (n.length > 0) {
                    i.unuse();
                    this.gemData.SacrificeNum--;
                    const r = $2Cfg.Cfg.EquipMergeLv.get($2GameUtil.GameUtil.getRandomByWeightInList(n)[0].id);
                    $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
                    $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs1_activation", {
                      nodeAttr: {
                        parent: this.packView.topEffectNode,
                        position: this.position
                      },
                      spAttr: {
                        animation: "animation",
                        type: $2GameSeting.GameSeting.TweenType.Not,
                        loop: false
                      },
                      delayRemove: 1
                    }, this.game.gameNode);
                    this.scheduleOnce(function () {
                      o.packView.newProp(r, {
                        position: o.position
                      }).then(function (e) {
                        e.setUnlock();
                        cc.tween(e.node).to(.2, {
                          scale: 1.3,
                          position: cc.Vec2.ZERO
                        }).by(.4, {
                          angle: 30
                        }).by(.4, {
                          angle: -30
                        }).call(function () {
                          let t: any;
                          null === (t = o.packView) || t === undefined || t.setInSpareBox(e);
                        }).start();
                      });
                      o.gemData.SacrificeNum <= 0 && o.unuse();
                    }, 1);
                  } else {
                    $2AlertManager.AlertManager.showNormalTips("只能献祭合成等级为2,3,4的装备");
                    this.packView.setInSpareBox(i);
                  }
                }
                break;
              case $2MBackpackHero.MBPack.ReactionType.MagicallyChange:
                if (0 == e.prem) {
                  return;
                }
                this.gemData.MagicallyChangeID = e.prem;
                const a = this.gemCfg;
                $2Manager.Manager.loader.loadSpriteToSprit(a.res, this.img, this.packView.node);
                this.gemData.SacrificeNum = 5;
                this.img.node.setAttribute({
                  active: true,
                  opacity: 0
                });
                $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
                $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs4_activation", {
                  nodeAttr: {
                    parent: this.packView.topEffectNode,
                    position: this.position
                  },
                  spAttr: {
                    animation: "animation",
                    type: $2GameSeting.GameSeting.TweenType.Not,
                    loop: false
                  }
                }, this.game.gameNode).then(function (e) {
                  o.mySkeleton.node.setActive(false);
                  cc.tween(e.node).delay(.5).call(function () {
                    cc.tween(o.img.node).stopLast().set({
                      active: true
                    }).to(.3, {
                      opacity: 255
                    }).to(.5, {
                      opacity: 100
                    }).union().repeatForever().start();
                    e.destroy();
                  }).start();
                });
                break;
              case $2MBackpackHero.MBPack.ReactionType.MinEquip:
                $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick);
                t = this.getWeight(i);
                if (i instanceof $2M20Prop_Equip.default) {
                  $2Manager.Manager.loader.loadSpineNode("bones/gem/buff_bs11_activation", {
                    delayRemove: 1,
                    nodeAttr: {
                      parent: this.packView.topEffectNode,
                      position: this.position
                    },
                    spAttr: {
                      animation: "animation" + (t ? 2 : 1),
                      type: $2GameSeting.GameSeting.TweenType.Not,
                      loop: false
                    }
                  }, this.game.gameNode);
                  this.packView.scheduleOnce(function () {
                    t && i.setMin();
                    $2AlertManager.AlertManager.showNormalTips(t ? "缩小成功" : "缩小失败");
                  }, 1);
                  this.unuse();
                }
                break;
              case $2MBackpackHero.MBPack.ReactionType.GeGeJi:
                this.gemData.eggOpenNum--;
                $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Fight_GeGeJi");
            }
            this.resetGemState();
            this.packView.scheduleOnce(function () {
              o.game.saveRecordVo();
            }, 2);
    }

    getWeight(e: any) {
        let t: any;
            let o: any;
            let i: any;
            const n = this;
            if (this.gemCfg.equipId == this.roleCfg.id) {
              (o = this.game.rVo.gemWeightList)[i = this.roleID] || (o[i] = 0);
              const r = null === (t = $2Manager.Manager.vo.switchVo.gemSuccessWeight.find(function (e) {
                return e[0] == n.game.level;
              }) || $2Manager.Manager.vo.switchVo.gemSuccessWeight.lastVal) || t === undefined ? undefined : t[1 + this.game.rVo.gemWeightList[this.roleID]];
              this.game.rVo.gemWeightList[this.roleID]++;
              if (r) {
                return $2GameUtil.GameUtil.weightFloat(r);
              }
            }
            if ([$2MBackpackHero.MBPack.ReactionType.CopyEquip, $2MBackpackHero.MBPack.ReactionType.MinEquip].includes(this.roleID)) {
              return $2GameUtil.GameUtil.weightFloat(this.gemCfg.ohterValue[e.mergeCfg.lv - 1]);
            } else {
              if ([$2MBackpackHero.MBPack.ReactionType.CopyGem].includes(this.roleID)) {
                return $2GameUtil.GameUtil.weightFloat(this.gemCfg.ohterValue[e.roleCfg.rarity - 1]);
              } else {
                return $2GameUtil.GameUtil.weightFloat(this.gemCfg.ohterValue[0]);
              }
            }
    }

    readData(t: any) {
        super.readData(t);
            this.gemData = t.gemData;
            if (this.roleCfg.id == $2MBackpackHero.MBPack.ReactionType.MagicallyChange && this.isUnlock) {
              this.setReaction({
                type: $2MBackpackHero.MBPack.ReactionType.MagicallyChange,
                item: this,
                prem: this.gemData.MagicallyChangeID
              });
            } else {
              this.resetGemState();
            }
    }

    showMosaicEffect(e: any) {
        $2Manager.Manager.audio.playAudio($2SoundCfg.SoundDefine.ui_addgem);
            $2Manager.Manager.loader.loadSpineNode("bones/gem/fx_bsset", {
              nodeAttr: {
                parent: e.node,
                position: cc.Vec2.ZERO
              },
              spAttr: {
                animation: "animation",
                type: $2GameSeting.GameSeting.TweenType.Not,
                loop: false
              },
              delayRemove: 1
            }, this.game.gameNode);
    }

    update() {
        const e = this;
            if (this.isValid && this.game) {
              this.lineBox.hideAllChildren();
              [$2MBackpackHero.MBPack.ReactionType.GreedyGemstone].includes(this.roleID) && this.lineTarget.forEach(function (t, o) {
                const i = $2GameUtil.GameUtil.GetAngle(e.node.position, t.node.position) + 90;
                const n = cc.Vec2.distance(e.node.position, t.node.position) / 393;
                (e.lineBox.children[o] || cc.instantiate(e.packView.linePrefab).setAttribute({
                  parent: e.lineBox
                })).setAttribute({
                  angle: i,
                  scale: n,
                  active: true
                });
              });
            }
    }

}
