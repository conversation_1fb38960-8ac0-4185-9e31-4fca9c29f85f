import $2Call<PERSON> from "./CallID";
import $2ListenID from "./ListenID";
import $2VideoButton from "./VideoButton";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2ModeChainsModel from "./ModeChainsModel";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2BuffCardItem from "./BuffCardItem";
import $2MChains from "./MChains";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeChains/M33_FightBuffView")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel)
export default class M33_FightBuffView extends $2Pop.Pop {
    @property(cc.Node)
    skillRoot: cc.Node = null;

    @property([cc.RichText])
    richTextArr: [cc.RichText] = null;

    @property(cc.Prefab)
    buffCardBarPrefab: cc.Prefab = null;

    get mode() {
        return $2ModeChainsModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    get param() {
        return this._openArgs.param;
    }

    get type() {
        return this.param.type || 1;
    }

    get poolData() {
        return this.game.rVo.poolADMap[this.type];
    }

    get resetNumDefault() {
        return this.game.recordVo.defaultData.poolADMap[this.type].resetNum;
    }

    get getAllNumDefault() {
        return this.game.recordVo.defaultData.poolADMap[this.type].getAll;
    }

    setInfo() {
        let e: any;
            const t = this.param.getPool();
            this.resetList(t);
            // this.labelArr[0].string = (null === (e = this.poolData) || e === undefined ? undefined : e.title) || ([$2MChains.MChains.poolType.DragonBall, $2MChains.MChains.poolType.HighBuff, $2MChains.MChains.poolType.InitialWeapon].includes(this.type) ? "请选择武器" : "选择强化属性");
    }

    resetList(e: any) {
        let t: any;
            const o = this;
            this.skillRoot.hideAllChildren();
            if (0 == e.length) {
              $2AlertManager.AlertManager.showNormalTips("欧皇!词条都被你抽完啦!");
              return void this.close();
            }
            this.selectIDList.length = 0;
            this.itemList.length = 0;
            this.bufflist = e.sort(function (e, t) {
              return e.isAd - t.isAd;
            });
            this.skillRoot.getComponent(cc.Layout).enabled = true;
            const i = this.bufflist.length;
            const n = function (e) {
              var i = r.bufflist[e];
              const n = $2Game.ModeCfg.Buff.get(i.id);
              const a = r.skillRoot.children[e] || cc.instantiate(r.buffCardBarPrefab).setAttribute({
                parent: r.skillRoot
              });
              a.setAttribute({
                scale: 0,
                active: true
              });
              const c = a.getComponent($2BuffCardItem.default);
              c.setInfo(n, i.isAd, e);
              c.eventScenc = "M" + $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode) + "_BuffView";
              r.itemList.push(c);
              r.scheduleOnce(function () {
                c.setClick(function () {
                  o.selectItem(i.id);
                });
              }, .3);
              (!t || n.rarity > t.rarity) && (t = c);
            };
            const r = this;
            for (const a = 0; a < i; a++) {
              n(a);
            }
            this.scheduleOnce(function () {
              o.skillRoot.getComponent(cc.Layout).enabled = false;
              2 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
                targetNode: t.node,
                descpos: 2
              });
            }, .3 * this.bufflist.length + .1);
            this.resetBtn();
    }

    selectItem(e: any) {
        this.selectIDList.push(e);
            if (this.selectIDList.length >= this.selectLimt) {
              this.itemList.forEach(function (e) {
                e.isCanClick = false;
              });
              this.selectIDList.splice(0, this.selectLimt).forEach(function (e) {
                $2Notifier.Notifier.send($2ListenID.ListenID.Fight_AddBuff, e);
              });
              this.close();
            }
            if (2 == $2Manager.Manager.vo.userVo.guideIndex) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
              $2Manager.Manager.vo.userVo.guideIndex = 11;
            }
    }

    resetBtn() {
        let e: any;
            let t: any;
            this.richTextArr[0].text = (null === (e = this.poolData) || e === undefined ? undefined : e.freeResetNum) ? cc.js.formatStr("剩余免费<color=#00ff00>%d</c>次", this.poolData.freeResetNum) : cc.js.formatStr("剩余次数<color=#00ff00>%d/%d</c>", this.resetNum, this.resetNumDefault);
            this.nodeArr[0].setActive(this.resetNum > 0 && this.bufflist.length > 2);
            this.nodeArr[0].getComponent($2VideoButton.default).isForFree = (null === (t = this.poolData) || t === undefined ? undefined : t.freeResetNum) > 0;
            this.richTextArr[1].text = cc.js.formatStr("剩余次数<color=#00ff00>%d/%d</c>", this.getAllNum, this.getAllNumDefault);
            this.nodeArr[1].setActive(this.getAllNum > 0 && this.bufflist.length > 1);
    }

    adResetAllBuff() {
        const e = true;
            if (this.poolData.freeResetNum > 0) {
              this.poolData.freeResetNum--;
              e = false;
            } else {
              this.poolData.resetNum--;
            }
            this.resetList(this.param.getPool(Object.assign(Object.assign({}, this.param), {
              isReset: true,
              isPay: e
            })));
            this.resetBtn();
    }

    adGetAllSkill() {
        this.bufflist.forEach(function (e) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Fight_AddBuff, e.id);
            });
            this.poolData.getAll--;
            this.close();
    }

}
