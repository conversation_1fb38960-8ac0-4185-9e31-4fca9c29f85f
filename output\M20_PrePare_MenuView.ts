import $2CallID from "./CallID";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2BaseSdk from "./BaseSdk";
import $2GameUtil from "./GameUtil";
import $2RecordVo from "./RecordVo";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_PrePare_MenuView")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Main)
@$2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Panel)
export default class M20_PrePare_MenuView extends cc.Component {
    @property(cc.Node)
    tweenbg: cc.Node = null;

    @property(cc.Node)
    viewnode: cc.Node = null;

    @property(cc.Node)
    menunodes: cc.Node = null;

    @property(cc.ToggleContainer)
    toggleContainer: cc.ToggleContainer = null;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    onOpen() {
        const e = this;
            const t = this.toggleContainer.toggleItems[2];
            const o = t.checkEvents[0].customEventData;
            o && "" != o && $2Notifier.Notifier.send($2ListenID.ListenID.M20_CheckMenu, o, {
              showCb: function (i) {
                i.setParent(e.viewnode);
                e.menulist.push({
                  name: o,
                  view: i
                });
                e.initMenu(t);
                for (const n = 0; n < e.toggleContainer.toggleItems.length; n++) {
                  2 != n && e.initViewList(n);
                }
              }
            });
            this.toggleContainer.toggleItems[1].node.getChildByName("lock").active = $2Manager.Manager.leveMgr.vo.curPassLv < 999;
            if ($2Manager.Manager.vo.userVo.guideIndex >= 17) {
              // 7 == this.mode.rVo.adSignList.length && 7 == this.mode.rVo.signIndex || this.mode.rVo.dailyData.isAdSign || this.mode.rVo.dailyData.isSign || $2Manager.Manager.leveMgr.vo.curPassLv >= $2Manager.Manager.vo.switchVo.dailysign && $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_SignIn", $2MVC.MVC.openArgs());
              const i = $2RecordVo.RecordVo.ReadData("MBackpackHero");
              i && $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_CoutinueFight", $2MVC.MVC.openArgs().setParam(i));
            }
            if ((window as any).wonderSdk.hasPay && $2Manager.Manager.vo.userVo.loginCount >= 1 && !$2Manager.Manager.Shop.Subscrib_Long && !$2Manager.Manager.Shop.Subscrib_30 && !$2Manager.Manager.vo.userVo.dailyData.Shop_Subscribe_DayMainShow) {
              $2UIManager.UIManager.OpenInQueue("ui/shop/Shop_SubscribeView");
              $2Manager.Manager.vo.userVo.dailyData.Shop_Subscribe_DayMainShow = true;
              $2Manager.Manager.vo.saveUserData();
            }
    }

    initViewList(e: any) {
        this.toggleContainer.toggleItems[e].checkEvents[0].customEventData;
    }

    initMenu(e: any) {
        this.tweenbg.opacity = 0;
            e.check();
            this.tweenbg.setActive(false);
            this.loadList(e.checkEvents[0].customEventData);
    }

    loadList(e: any) {
        const t = this;
            this.menulist.forEach(function (t) {
              t.view.zIndex = t.name == e ? 1 : 0;
              t.name == e && (t.view.active = true);
              cc.tween(t.view).stopLast().set({
                x: 0,
                y: 0
              }).to(.1, {
                opacity: t.name == e ? 255 : 0
              }).set({
                active: t.name == e
              }).start();
            });
            this.curmenu = e;
            15 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
            $2Notifier.Notifier.send($2ListenID.ListenID.M20_CheckMenu, e, {
              showCb: function (o) {
                o.setParent(t.viewnode);
                t.menulist.push({
                  name: e,
                  view: o
                });
              }
            });
    }

    menuCheck(e: any) {
        const t = this;
            const o = e.checkEvents[0].customEventData;
            if (!o || "Role" == o || "Activity" == o) {
              $2AlertManager.AlertManager.showNormalTips("敬请期待");
              return void this.toggleContainer.toggleItems.find(function (e) {
                return e.checkEvents[0].customEventData == t.curmenu;
              }).check();
            }
            if (this.curmenu != o) {
              if ("Role" == o && $2Manager.Manager.leveMgr.vo.curPassLv < 3) {
                $2AlertManager.AlertManager.showNormalTips("通关第三章解锁");
                return void this.toggleContainer.toggleItems.find(function (e) {
                  return e.checkEvents[0].customEventData == t.curmenu;
                }).check();
              }
              if ((window as any).wonderSdk.hasPay && 0 == $2Manager.Manager.vo.userVo.loginCount && "Shop" == this.oldTap && "Fight" == o && !$2Manager.Manager.vo.knapsackVo.has("Shop_SubscribeView_MainShow")) {
                $2UIManager.UIManager.OpenInQueue("ui/shop/Shop_SubscribeView");
                $2Manager.Manager.vo.knapsackVo.addGoods("Shop_SubscribeView_MainShow", $2GameSeting.GameSeting.GoodsType.System);
              }
              cc.Tween.stopAllByTarget(this.tweenbg);
              cc.tween(this.tweenbg).set({
                active: true
              }).to(.1, {
                opacity: 255
              }).to(.1, {
                opacity: 0
              }).call(function () {
                t.tweenbg.setActive(false);
              }).start();
              this.loadList(o);
              this.oldTap = o;
            }
    }

    changeListener(e: any) {
        $2Notifier.Notifier.changeCall(e, $2CallID.CallID.M20_GetMenuView, this.getMyself, this);
            $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.JumpOpenMainView, this.jumpOpenMainView, this);
    }

    jumpOpenMainView(e: any) {
        this.toggleContainer.toggleItems[e].check();
            this.menuCheck(this.toggleContainer.toggleItems[e]);
    }

    getMyself() {
        return this;
    }

    onBtn(e: any, t: any, o: any) {
        return cc__awaiter(this, undefined, undefined, function () {
              let e: any;
              let i: any;
              let n: any;
              let r: any;
              let a: any;
              let l: any;
              const m = this;
              return cc__generator(this, function () {
                if (t.includes("ui/")) {
                  $2UIManager.UIManager.OpenInQueue(t, $2MVC.MVC.openArgs());
                  return [2];
                }
                switch (t) {
                  case "mode_5":
                  case "mode_20":
                  case "mode_33":
                    e = $2Manager.Manager.vo.switchVo.fightStamina;
                    if (!this.mode.currencyIsEnough($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, e)) {
                      return [2, $2AlertManager.AlertManager.showNormalTips("体力不足")];
                    }
                    if (this.mode.userEquipPack.filter(function (e) {
                      return e.isFitOut;
                    }).length < 8) {
                      $2AlertManager.AlertManager.showNormalTips("装备数量不足");
                      this.toggleContainer.toggleItems[3].check();
                      this.menuCheck(this.toggleContainer.toggleItems[3]);
                      return [2];
                    }
                    i = $2UIManager.UIManager.getView("ui/ModeBackpackHero/M20_PrePare_Fight");
                    $2Manager.Manager.vo.knapsackVo.useUp($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, e);
                    $2Notifier.Notifier.send($2ListenID.ListenID.Item_GoodsChange);
                    n = +t.split("_")[1];
                    r = $2Game.Game.getMouth(n);
                    $2Notifier.Notifier.send(r.mouth, n, $2MVC.MVC.openArgs().setParam({
                      id: i.curSelectPass.lvid
                    }));
                    break;
                  case "getDiamond":
                  case "getCoin":
                    this.toggleContainer.toggleItems[0].check();
                    this.menuCheck(this.toggleContainer.toggleItems[0]);
                    a = $2Notifier.Notifier.call($2CallID.CallID.M20_GetShopView);
                    if (l = null == a ? undefined : a.getComponentInChildren(cc.ScrollView)) {
                      if ("getDiamond" == t) {
                        l.scrollToPercentVertical(1, .2);
                      } else {
                        l.scrollToPercentVertical(0, .2);
                      }
                    }
                    break;
                  case "TTTTTTTTTTs":
                    $2Notifier.Notifier.send($2ListenID.ListenID.Test_OpenView);
                    break;
                  case "openSetting":
                    $2Notifier.Notifier.send($2ListenID.ListenID.Setting_OpenView);
                    break;
                  case "sweeping":
                    if (0 !== $2Manager.Manager.leveMgr.vo.dailyData.sweep_count && !this.mode.currencyIsEnough($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, $2Manager.Manager.vo.switchVo.fightStamina)) {
                      return [2, $2AlertManager.AlertManager.showNormalTips("体力不足")];
                    }
                    if (0 !== $2Manager.Manager.leveMgr.vo.dailyData.sweep_count) {
                      $2Manager.Manager.vo.knapsackVo.useUp($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, $2Manager.Manager.vo.switchVo.fightStamina);
                      $2Notifier.Notifier.send($2ListenID.ListenID.Item_GoodsChange);
                      $2Manager.Manager.leveMgr.vo.dailyData.sweep_count--;
                    } else {
                      0 == $2Manager.Manager.leveMgr.vo.dailyData.sweep_count && $2Manager.Manager.leveMgr.vo.dailyData.sweep_count_ad--;
                    }
                    m.mode.recordVo.SaveData();
                    null == o || o();
                    break;
                  case "PlatformTTSidebarRewardView":
                    if ((window as any).wonderSdk.isByteDance) {
                      $2UIManager.UIManager.Open("ui/platform/PlatformTTSidebarRewardView", $2MVC.MVC.openArgs().setParam({
                        curCheck: 1
                      }));
                    } else {
                      (window as any).wonderSdk.isBLMicro && $2UIManager.UIManager.Open("ui/platform/PlatformBLSidebarRewardView", $2MVC.MVC.openArgs().setParam({
                        curCheck: 1
                      }));
                    }
                    break;
                  case "PlatformTTDeskRewardView":
                    if ((window as any).wonderSdk.isByteDance) {
                      $2UIManager.UIManager.Open("ui/platform/PlatformTTSidebarRewardView", $2MVC.MVC.openArgs().setParam({
                        curCheck: 2
                      }));
                    } else {
                      (window as any).wonderSdk.isBLMicro && $2UIManager.UIManager.Open("ui/platform/PlatformBLSidebarRewardView", $2MVC.MVC.openArgs().setParam({
                        curCheck: 2
                      }));
                    }
                    break;
                  case "showsignin":
                    $2Manager.Manager.leveMgr.vo.curPassLv >= $2Manager.Manager.vo.switchVo.dailysign && $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_SignIn", $2MVC.MVC.openArgs());
                    break;
                  case "showExchangeCodeView":
                    $2Notifier.Notifier.send($2ListenID.ListenID.Activity_OpenExchangeCode, true);
                    break;
                  case "Share":
                    (window as any).wonderSdk.share($2BaseSdk.ShareType.SHARE_NORMAL, {});
                }
                return [2];
              });
            });
    }

    setInfo() {
        if (11 == $2Manager.Manager.vo.userVo.guideIndex) {
              const e = this;
              this.scheduleOnce(function () {
                $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
                  targetNode: e.toggleContainer.toggleItems[3].node,
                  tweencb: function () {
                    $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Anim, e.toggleContainer.toggleItems[3].node, e.toggleContainer.toggleItems[3].node, cc.v2(0, 0), null, true);
                  }
                });
              }, .5);
            }
            this.node.setContentSize($2GameUtil.GameUtil.getDesignSize);
    }

}
