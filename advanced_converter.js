const fs = require('fs');
const path = require('path');

class AdvancedJSToTSConverter {
    constructor() {
        this.patterns = {
            // 模块导入模式
            imports: /var \$2(\w+) = require\("(\w+)"\);/g,
            
            // 类定义模式
            classDefinition: /var def_(\w+) = function \(e\) \{/,
            classEnd: /\}\(\$2(\w+)\.(\w+)\);/,
            
            // 装饰器模式
            decorators: {
                ccclass: /ccp_ccclass/,
                property: /cc__decorate\(\[ccp_property\(([^)]+)\)\], _ctor\.prototype, "(\w+)", undefined\);/g,
                menu: /ccp_menu\("([^"]+)"\)/,
                mvc: /\$2MVC\.MVC\.(\w+)\(\$2MVC\.MVC\.(\w+)\.(\w+)\)/g
            },
            
            // 方法模式
            methods: {
                prototype: /_ctor\.prototype\.(\w+) = function \(([^)]*)\) \{/g,
                getter: /Object\.defineProperty\(_ctor\.prototype, "(\w+)", \{\s*get: function \(\) \{\s*return ([^;]+);\s*\}/g,
                async: /__awaiter\(this, undefined, undefined, function \(\) \{/g
            },
            
            // 构造函数模式
            constructor: /function _ctor\(\) \{\s*var t = null !== e && e\.apply\(this, arguments\) \|\| this;([\s\S]*?)return t;\s*\}/,
            
            // 属性初始化模式
            propertyInit: /t\.(\w+) = ([^;]+);/g
        };
        
        this.typeMap = new Map([
            ['cc.Node', 'cc.Node'],
            ['cc.Label', 'cc.Label'],
            ['cc.Sprite', 'cc.Sprite'],
            ['cc.Button', 'cc.Button'],
            ['cc.Prefab', 'cc.Prefab'],
            ['cc.RichText', 'cc.RichText'],
            ['sp.Skeleton', 'sp.Skeleton'],
            ['cc.Component', 'cc.Component']
        ]);
    }

    // 解析完整的文件结构
    parseFileStructure(content) {
        const structure = {
            imports: [],
            className: '',
            baseClass: '',
            decorators: [],
            properties: [],
            methods: [],
            getters: [],
            constructor: null
        };

        // 解析导入
        structure.imports = this.parseImports(content);
        
        // 解析类信息
        const classInfo = this.parseClassInfo(content);
        if (classInfo) {
            structure.className = classInfo.className;
            structure.baseClass = classInfo.baseClass;
        }

        // 解析装饰器
        structure.decorators = this.parseAllDecorators(content);
        
        // 解析属性
        structure.properties = this.parseAllProperties(content);
        
        // 解析方法
        structure.methods = this.parseAllMethods(content);
        
        // 解析 getter
        structure.getters = this.parseGetters(content);
        
        // 解析构造函数
        structure.constructor = this.parseConstructor(content);

        return structure;
    }

    parseImports(content) {
        const imports = [];
        let match;
        
        while ((match = this.patterns.imports.exec(content)) !== null) {
            imports.push({
                moduleName: `$2${match[1]}`,
                fileName: `$2${match[2]}`
            });
        }
        
        return imports;
    }

    parseClassInfo(content) {
        const classMatch = content.match(this.patterns.classDefinition);
        const endMatch = content.match(this.patterns.classEnd);
        
        if (!classMatch) return null;
        
        const className = classMatch[1];
        let baseClass = 'cc.Component';
        
        if (endMatch) {
            baseClass = `$2${endMatch[1]}.${endMatch[2]}`;
        }
        
        return { className, baseClass };
    }

    parseAllDecorators(content) {
        const decorators = [];
        
        // ccclass
        if (this.patterns.decorators.ccclass.test(content)) {
            decorators.push('@ccclass');
        }
        
        // menu
        const menuMatch = content.match(this.patterns.decorators.menu);
        if (menuMatch) {
            decorators.push(`@menu("${menuMatch[1]}")`);
        }
        
        // MVC decorators
        let mvcMatch;
        while ((mvcMatch = this.patterns.decorators.mvc.exec(content)) !== null) {
            decorators.push(`@$2MVC.MVC.${mvcMatch[1]}($2MVC.MVC.${mvcMatch[2]}.${mvcMatch[3]})`);
        }
        
        return decorators;
    }

    parseAllProperties(content) {
        const properties = [];
        let match;
        
        while ((match = this.patterns.decorators.property.exec(content)) !== null) {
            const propType = match[1];
            const propName = match[2];
            
            properties.push({
                name: propName,
                type: this.typeMap.get(propType) || propType,
                decorator: `@property(${propType})`
            });
        }
        
        return properties;
    }

    parseAllMethods(content) {
        const methods = [];
        let match;
        
        while ((match = this.patterns.methods.prototype.exec(content)) !== null) {
            const methodName = match[1];
            const params = match[2];
            
            // 查找方法体
            const methodBodyStart = content.indexOf(match[0]);
            const methodBody = this.extractMethodBody(content, methodBodyStart);
            
            methods.push({
                name: methodName,
                params: this.parseParameters(params),
                body: this.convertMethodBody(methodBody),
                isAsync: this.isAsyncMethod(methodBody)
            });
        }
        
        return methods;
    }

    parseGetters(content) {
        const getters = [];
        let match;
        
        while ((match = this.patterns.methods.getter.exec(content)) !== null) {
            const getterName = match[1];
            const returnValue = match[2];
            
            getters.push({
                name: getterName,
                returnValue: this.convertExpression(returnValue)
            });
        }
        
        return getters;
    }

    parseConstructor(content) {
        const match = content.match(this.patterns.constructor);
        if (!match) return null;
        
        const constructorBody = match[1];
        const properties = [];
        let propMatch;
        
        while ((propMatch = this.patterns.propertyInit.exec(constructorBody)) !== null) {
            properties.push({
                name: propMatch[1],
                value: this.convertExpression(propMatch[2])
            });
        }
        
        return { properties };
    }

    // 辅助方法
    extractMethodBody(content, startIndex) {
        let braceCount = 0;
        let i = startIndex;
        let foundStart = false;
        
        while (i < content.length) {
            if (content[i] === '{') {
                braceCount++;
                foundStart = true;
            } else if (content[i] === '}') {
                braceCount--;
                if (foundStart && braceCount === 0) {
                    break;
                }
            }
            i++;
        }
        
        return content.substring(startIndex, i + 1);
    }

    parseParameters(params) {
        if (!params.trim()) return [];
        
        return params.split(',').map(param => {
            const trimmed = param.trim();
            return {
                name: trimmed,
                type: 'any' // 默认类型，可以根据需要改进
            };
        });
    }

    convertMethodBody(body) {
        // 移除函数声明部分
        body = body.replace(/^[^{]*\{/, '').replace(/\}$/, '');
        
        // 应用各种转换规则
        body = this.applyConversionRules(body);
        
        return body.trim();
    }

    convertExpression(expr) {
        return this.applyConversionRules(expr);
    }

    applyConversionRules(code) {
        // 替换 super 调用
        code = code.replace(/e\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$1($2)');
        
        // 替换变量声明
        code = code.replace(/var (\w+);/g, 'let $1: any;');
        code = code.replace(/var (\w+) = ([^;]+);/g, 'const $1 = $2;');
        
        // 替换比较操作
        code = code.replace(/undefined === (\w+)/g, '$1 === undefined');
        code = code.replace(/null === (\w+)/g, '$1 === null');
        
        // 替换工具函数
        code = code.replace(/cc__assign/g, 'Object.assign');
        code = code.replace(/cc__spreadArrays\(([^)]+)\)/g, '[...$1]');
        
        // 替换全局对象
        code = code.replace(/\bwonderSdk\b/g, '(window as any).wonderSdk');
        
        // 替换模块引用
        code = code.replace(/\$2(\w+)/g, '$2$1');
        
        return code;
    }

    isAsyncMethod(body) {
        return this.patterns.methods.async.test(body);
    }

    // 生成 TypeScript 代码
    generateTypeScript(structure) {
        let tsCode = '';
        
        // 生成导入
        structure.imports.forEach(imp => {
            tsCode += `import ${imp.moduleName} from "./${imp.fileName}";\n`;
        });
        tsCode += '\n';
        
        // 生成装饰器常量
        tsCode += 'const { ccclass, property, menu } = cc._decorator;\n\n';
        
        // 生成类装饰器
        structure.decorators.forEach(decorator => {
            tsCode += decorator + '\n';
        });
        
        // 生成类定义
        tsCode += `export default class ${structure.className} extends ${structure.baseClass} {\n`;
        
        // 生成属性
        structure.properties.forEach(prop => {
            tsCode += `    ${prop.decorator}\n`;
            tsCode += `    ${prop.name}: ${prop.type} = null;\n\n`;
        });
        
        // 生成构造函数属性
        if (structure.constructor) {
            structure.constructor.properties.forEach(prop => {
                tsCode += `    ${prop.name}: any = ${prop.value};\n`;
            });
            tsCode += '\n';
        }
        
        // 生成 getter
        structure.getters.forEach(getter => {
            tsCode += `    get ${getter.name}() {\n`;
            tsCode += `        return ${getter.returnValue};\n`;
            tsCode += `    }\n\n`;
        });
        
        // 生成方法
        structure.methods.forEach(method => {
            const asyncKeyword = method.isAsync ? 'async ' : '';
            const params = method.params.map(p => `${p.name}: ${p.type}`).join(', ');
            
            tsCode += `    ${asyncKeyword}${method.name}(${params}) {\n`;
            
            // 缩进方法体
            const indentedBody = method.body.split('\n')
                .map(line => line ? '        ' + line : line)
                .join('\n');
            
            tsCode += indentedBody + '\n';
            tsCode += `    }\n\n`;
        });
        
        tsCode += '}\n';
        
        return tsCode;
    }

    // 转换文件
    convertFile(inputPath, outputPath) {
        try {
            const jsContent = fs.readFileSync(inputPath, 'utf8');
            const structure = this.parseFileStructure(jsContent);
            
            if (!structure.className) {
                console.warn(`❌ 无法解析类定义: ${path.basename(inputPath)}`);
                return false;
            }
            
            const tsContent = this.generateTypeScript(structure);
            fs.writeFileSync(outputPath, tsContent, 'utf8');
            
            console.log(`✅ 转换成功: ${path.basename(inputPath)} -> ${path.basename(outputPath)}`);
            return true;
        } catch (error) {
            console.error(`❌ 转换出错 ${path.basename(inputPath)}:`, error.message);
            return false;
        }
    }

    // 批量转换
    batchConvert(inputDir, outputDir) {
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const files = fs.readdirSync(inputDir).filter(file => file.endsWith('.js'));
        let successCount = 0;

        console.log(`🚀 开始批量转换 ${files.length} 个文件...\n`);

        files.forEach(file => {
            const inputPath = path.join(inputDir, file);
            const outputPath = path.join(outputDir, file.replace('.js', '.ts'));
            
            if (this.convertFile(inputPath, outputPath)) {
                successCount++;
            }
        });

        console.log(`\n🎉 转换完成! 成功: ${successCount}/${files.length}`);
        
        if (successCount < files.length) {
            console.log(`\n⚠️  有 ${files.length - successCount} 个文件转换失败，可能需要手动处理`);
        }
    }
}

// 使用示例
if (require.main === module) {
    const converter = new AdvancedJSToTSConverter();
    
    // 检查命令行参数
    const args = process.argv.slice(2);
    const inputDir = args[0] || './scripts';
    const outputDir = args[1] || './output';
    
    console.log(`📁 输入目录: ${inputDir}`);
    console.log(`📁 输出目录: ${outputDir}\n`);
    
    converter.batchConvert(inputDir, outputDir);
}

module.exports = AdvancedJSToTSConverter;
