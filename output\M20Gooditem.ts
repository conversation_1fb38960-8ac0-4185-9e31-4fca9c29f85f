import $2Manager from "./Manager";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20<PERSON>ooditem extends cc.Component {
    @property(cc.Label)
    Count: cc.Label = null;

    @property(cc.Sprite)
    goodsbg: cc.Sprite = null;

    @property(cc.Sprite)
    goodimg: cc.Sprite = null;

    setdata(e: any) {
        let t: any;
            this.Count.node.setActive(e.count);
            this.Count.string = null !== (t = e.count) && undefined !== t ? t : 0;
            $2Manager.Manager.loader.loadSpriteToSprit(e.path, this.goodimg, this.node.parent);
            if (!e.bgpath) {
              const img = this.node.getChildByName("img").getComponent(cc.Sprite);
              img.sizeMode = cc.Sprite.SizeMode.RAW;
            }
            this.node.getChildByName("icon_pintu").active = e.isfrag;
            this.goodsbg.node.active = e.bgpath;
            $2Manager.Manager.loader.loadSpriteToSprit(e.bgpath, this.goodsbg, this.node.parent);
    }

}
