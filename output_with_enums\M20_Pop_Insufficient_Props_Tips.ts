import $2Call<PERSON> from "./CallID";
import $2GameSeting from "./GameSeting";
import $2L<PERSON>en<PERSON> from "./ListenID";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_Pop_Insufficient_Props_Tips")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup)
export default class M20_Pop_Insufficient_Props_Tips extends $2Pop.Pop {
    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    setInfo() {
        const e = this;
            this.currencyType = this.param.currencyType;
            const t = $2Cfg.Cfg.CurrencyConfig.get(this.currencyType);
            this.labelArr[0].string = t.name;
            this.labelArr[1].string = t.desc;
            $2Manager.Manager.loader.loadSpriteAsync(t.icon).then(function (t) {
              e.nodeArr[5].getComponent(cc.Sprite).spriteFrame = t;
            });
            const o = $2GameSeting.GameSeting.getRarity(this.mode.buffmap[t.rarity]).blockImg;
            $2Manager.Manager.loader.loadSpriteAsync(o).then(function (t) {
              e.nodeArr[7].getComponent(cc.Sprite).spriteFrame = t;
            });
            this.nodeArr[4].children.forEach(function (e) {
              e.active = false;
            });
            this.nodeArr[0].active = true;
            this.nodeArr[1].active = this.mode.rVo.curPassLv > 0 && (this.mode.rVo.dailyData.sweep_count > 0 || $2Manager.Manager.vo.switchVo.lvSweep[2] > 0);
            if (this.currencyType == $2CurrencyConfigCfg.CurrencyConfigDefine.Coin) {
              this.nodeArr[6].active = false;
              this.labelArr[2].string = "灵石不足";
              this.nodeArr[0].getComByChild(cc.Label).string = "商城获取灵石";
              this.nodeArr[2].active = this.mode.CurChallengeLv - 1 >= $2Cfg.Cfg.activity.get(3).unlockChapter;
            } else {
              this.nodeArr[6].active = true;
              this.labelArr[2].string = "碎片不足";
              this.nodeArr[0].getComByChild(cc.Label).string = "商城开箱子";
              console.log("unlockChapter: " + $2Cfg.Cfg.activity.get(2).unlockChapter);
              this.nodeArr[3].active = this.mode.CurChallengeLv - 1 >= $2Cfg.Cfg.activity.get(2).unlockChapter;
            }
    }

    onBtn(e: any, t: any) {
        console.log("onBtn: " + t);
            switch (t) {
              case "shop":
                $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain);
                const o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView);
                o.initMenu(o.toggleContainer.toggleItems[0]);
                const i = $2Notifier.Notifier.call($2CallID.CallID.M20_GetShopView);
                const n = null == i ? undefined : i.getComponentInChildren(cc.ScrollView);
                n && n.scrollToPercentVertical(0, .2);
                this.close();
                $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_EquipInfo");
                $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_RoleInfo");
                break;
              case "sweep":
                $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain);
                (o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView)).initMenu(o.toggleContainer.toggleItems[2]);
                this.close();
                $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_EquipInfo");
                $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_RoleInfo");
                break;
              case "coin_challenge":
                $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain);
                (o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView)).initMenu(o.toggleContainer.toggleItems[4]);
                this.close();
                $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_EquipInfo");
                $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_RoleInfo");
                const r = $2Cfg.Cfg.activity.get(3).prefab;
                r.includes("ui/") && $2UIManager.UIManager.OpenInQueue(r, $2MVC.MVC.openArgs());
                break;
              case "challenge":
                $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain);
                (o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView)).initMenu(o.toggleContainer.toggleItems[4]);
                this.close();
                $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_EquipInfo");
                $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Pop_RoleInfo");
                (r = $2Cfg.Cfg.activity.get(2).prefab).includes("ui/") && $2UIManager.UIManager.OpenInQueue(r, $2MVC.MVC.openArgs());
            }
    }

}
