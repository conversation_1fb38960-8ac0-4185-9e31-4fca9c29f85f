# Cocos Creator 2.4.15 JS to TS 批量转换工具

这个工具可以将 Cocos Creator 2.4.15 编译后的 JavaScript 文件批量还原为 TypeScript 源码。

## 功能特点

✅ **完整的语法转换**
- 自动识别并转换 Cocos Creator 的编译模式
- 保留 `$2` 模块前缀（按您的要求）
- 正确还原装饰器语法 (`@ccclass`, `@property`, `@menu` 等)
- 转换继承关系和类定义
- 还原方法、属性和 getter

✅ **智能代码转换**
- `require()` → `import` 语法
- `var` → `const/let` 声明
- `cc__assign` → `Object.assign`
- `cc__spreadArrays` → 展开运算符 `[...]`
- `wonderSdk` → `(window as any).wonderSdk`
- 自动添加 TypeScript 类型注解

✅ **批量处理**
- 一次性转换整个文件夹
- 自动创建输出目录
- 详细的转换进度和结果报告
- 错误处理和日志记录

## 使用方法

### 1. 基础版本 (batch_convert.js)

```bash
# 安装 Node.js (如果还没有)
# 然后运行转换脚本

node batch_convert.js
```

这会将 `./scripts` 文件夹中的所有 `.js` 文件转换为 TypeScript，输出到 `./output` 文件夹。

### 2. 高级版本 (advanced_converter.js)

```bash
# 使用默认路径
node advanced_converter.js

# 或指定自定义路径
node advanced_converter.js ./your-input-dir ./your-output-dir
```

### 3. 转换单个文件

```javascript
const AdvancedJSToTSConverter = require('./advanced_converter');
const converter = new AdvancedJSToTSConverter();

// 转换单个文件
converter.convertFile('./scripts/M20_PartItem.js', './output/M20_PartItem.ts');
```

## 转换示例

### 输入 (JavaScript)
```javascript
var $2Manager = require("Manager");
var $2Pop = require("Pop");

var def_M20_Example = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.myNode = null;
    return t;
  }
  cc__extends(_ctor, e);
  
  _ctor.prototype.onLoad = function () {
    console.log("Hello World");
  };
  
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "myNode", undefined);
  return cc__decorate([ccp_ccclass], _ctor);
}($2Pop.Pop);
```

### 输出 (TypeScript)
```typescript
import $2Manager from "./$2Manager";
import $2Pop from "./$2Pop";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_Example extends $2Pop.Pop {
    @property(cc.Node)
    myNode: cc.Node = null;

    onLoad() {
        console.log("Hello World");
    }
}
```

## 支持的转换模式

### 1. 模块导入
- ✅ `var $2Module = require("Module")` → `import $2Module from "./$2Module"`
- ✅ 保留 `$2` 前缀

### 2. 类定义
- ✅ `var def_ClassName = function(e) {...}` → `export default class ClassName`
- ✅ 继承关系: `}($2Pop.Pop)` → `extends $2Pop.Pop`

### 3. 装饰器
- ✅ `ccp_ccclass` → `@ccclass`
- ✅ `ccp_property(cc.Node)` → `@property(cc.Node)`
- ✅ `ccp_menu("path")` → `@menu("path")`
- ✅ MVC 装饰器: `$2MVC.MVC.uilayer(...)` → `@$2MVC.MVC.uilayer(...)`

### 4. 方法和属性
- ✅ 原型方法转换
- ✅ getter 方法转换
- ✅ 属性初始化
- ✅ 异步方法识别

### 5. 语法转换
- ✅ `var` → `const/let`
- ✅ `e.prototype.method.call(this, ...)` → `super.method(...)`
- ✅ `cc__assign` → `Object.assign`
- ✅ `cc__spreadArrays` → `[...]`
- ✅ 类型注解添加

## 注意事项

1. **备份原文件**: 转换前请备份您的原始文件
2. **检查结果**: 转换后请检查生成的 TypeScript 代码
3. **手动调整**: 复杂的逻辑可能需要手动调整
4. **类型完善**: 可能需要手动添加更精确的类型定义

## 已测试的文件类型

✅ 基础组件类 (继承自 cc.Component)
✅ 弹窗类 (继承自 Pop)
✅ UI 组件类
✅ 商店物品类
✅ 管理器类

## 故障排除

### 转换失败的常见原因
1. **文件格式不标准**: 某些手动修改过的 JS 文件可能无法识别
2. **复杂的异步逻辑**: 包含复杂 generator 函数的文件
3. **特殊语法**: 使用了非标准编译输出的文件

### 解决方案
1. 检查控制台输出的错误信息
2. 对于失败的文件，可以参考成功转换的文件手动调整
3. 使用基础版本 `batch_convert.js` 作为备选方案

## 扩展功能

如果需要添加新的转换规则，可以修改 `advanced_converter.js` 中的：
- `patterns` 对象：添加新的正则表达式模式
- `applyConversionRules` 方法：添加新的转换规则
- `typeMap`：添加新的类型映射

## 技术支持

如果遇到问题或需要添加新功能，请提供：
1. 失败的 JS 文件示例
2. 期望的 TS 输出格式
3. 错误日志信息

这样可以帮助改进转换工具的准确性和覆盖范围。
