import $2GameSeting from "./GameSeting";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Manager from "./Manager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20Gooditem from "./M20Gooditem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_Pop_GameRewardView")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Guide)
export default class M20_Pop_GameRewardView extends $2Pop.Pop {
    @property(cc.Node)
    rewardNode: cc.Node = null;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    onClickFrame() {
        this.close();
    }

    changeListener(t: any) {
        super.changeListener(t);
    }

    onOpen() {
        const e = this;
            $2Manager.Manager.loader.loadPrefabRes("ui/ModeBackpackHero/goodItem").then(function (t) {
              e.param.data.forEach(function (o, i) {
                const n = cc.instantiate(t);
                n.setParent(e.rewardNode);
                cc.tween(n).set({
                  opacity: 0
                }).delay(.2 * i).to(.2, {
                  opacity: 255
                }).start();
                const r = "";
                const c = $2GameSeting.GameSeting.getRarity(o.rarity).blockImg;
                if (o.type == $2GameSeting.GameSeting.GoodsType.Fragment) {
                  const l = $2Cfg.Cfg.RoleUnlock.find({
                    id: o.id
                  });
                  r = l.icon;
                  c = $2GameSeting.GameSeting.getRarity(l.rarity).blockImg;
                } else {
                  r = $2Cfg.Cfg.CurrencyConfig.get(o.id).icon;
                }
                n.getComponent($2M20Gooditem.default).setdata({
                  path: r,
                  bgpath: c,
                  count: o.num,
                  isfrag: o.type == $2GameSeting.GameSeting.GoodsType.Fragment
                });
              });
            });
    }

    setInfo() {
        $2Manager.Manager.audio.playAudio(2002);
    }

}
