import $2GameSeting from "./$2GameSeting";
import $2Cfg from "./$2Cfg";
import $2MVC from "./$2MVC";
import $2Pop from "./$2Pop";
import $2Manager from "./$2Manager";
import $2EaseScaleTransition from "./$2EaseScaleTransition";
import $2Game from "./$2Game";
import $2ModeBackpackHeroModel from "./$2ModeBackpackHeroModel";
import $2M20Gooditem from "./$2M20Gooditem";

const { ccclass, property, menu } = cc._decorator;

interface RewardData {
    id: number;
    num: number;
    type: number;
    rarity: number;
}

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_Pop_GameRewardView")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Guide)
@$2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)
export default class M20_Pop_GameRewardView extends $2Pop.Pop {
    @property(cc.Node)
    rewardNode: cc.Node = null;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    onClickFrame() {
        this.close();
    }

    changeListener(isAdd: boolean) {
        super.changeListener(isAdd);
    }

    onOpen() {
        $2Manager.Manager.loader.loadPrefabRes("ui/ModeBackpackHero/goodItem").then((prefab: cc.Node) => {
            this.param.data.forEach((reward: RewardData, index: number) => {
                const itemNode = cc.instantiate(prefab);
                itemNode.setParent(this.rewardNode);
                
                cc.tween(itemNode)
                    .set({ opacity: 0 })
                    .delay(0.2 * index)
                    .to(0.2, { opacity: 255 })
                    .start();

                let iconPath = "";
                let bgPath = $2GameSeting.GameSeting.getRarity(reward.rarity).blockImg;
                
                if (reward.type === $2GameSeting.GameSeting.GoodsType.Fragment) {
                    const roleUnlock = $2Cfg.Cfg.RoleUnlock.find({ id: reward.id });
                    iconPath = roleUnlock.icon;
                    bgPath = $2GameSeting.GameSeting.getRarity(roleUnlock.rarity).blockImg;
                } else {
                    iconPath = $2Cfg.Cfg.CurrencyConfig.get(reward.id).icon;
                }

                itemNode.getComponent($2M20Gooditem.default).setdata({
                    path: iconPath,
                    bgpath: bgPath,
                    count: reward.num,
                    isfrag: reward.type === $2GameSeting.GameSeting.GoodsType.Fragment
                });
            });
        });
    }

    onClose() {}

    setInfo() {
        $2Manager.Manager.audio.playAudio(2002);
    }
}
