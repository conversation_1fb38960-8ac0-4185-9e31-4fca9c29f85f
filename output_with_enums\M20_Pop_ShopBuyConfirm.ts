import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20Gooditem from "./M20Gooditem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_Pop_ShopBuyConfirm")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup)
export default class M20_Pop_ShopBuyConfirm extends $2Pop.Pop {
    @property(cc.Node)
    shownode: cc.Node = null;

    @property(cc.Label)
    cost: cc.Label = null;

    @property(cc.Sprite)
    costT: cc.Sprite = null;

    @property(cc.Label)
    desc: cc.Label = null;

    @property(cc.Label)
    title: cc.Label = null;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    setInfo() {
        const e = this;
            const t = this.param.cfg;
            const o = $2Cfg.Cfg.RoleUnlock.find({
              id: t.equipId
            });
            const i = this.param.disCount;
            const n = this.param.count || t.getNum;
            const r = $2Cfg.Cfg.CurrencyConfig.get(t.type);
            $2Manager.Manager.loader.loadPrefab("ui/ModeBackpackHero/goodItem").then(function (i) {
              const r = i;
              r.setParent(e.shownode);
              const s = r.getComponent($2M20Gooditem.default);
              s.setdata({
                path: o ? o.icon : t.icon,
                bgpath: o ? $2GameSeting.GameSeting.getRarity(e.mode.buffmap[o.rarity]).blockImg : t.bgpath,
                count: n,
                isfrag: !!o
              });
              s.goodsbg.node.setActive(!!o);
            });
            this.cost.string = (o ? Math.floor(t.costVal[0] * i) : " 获得") + "";
            const s = $2Cfg.Cfg.CurrencyConfig.find({
              id: t.costType[0]
            });
            $2Manager.Manager.loader.loadSpriteToSprit(s.icon, this.costT, this.node);
            // this.costT.node.scale = 22 == t.type ? 1 : .6;
            this.desc.string = o ? o.desc : r.desc;
            this.title.string = t.title;
    }

    buyIt() {
        let e: any;
            let t: any;
            $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 3);
            null === (t = (e = this.param).cb) || t === undefined || t.call(e);
            this.close();
    }

    onClickFrame() {
        this.close();
    }

}
