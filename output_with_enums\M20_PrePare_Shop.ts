import $2Call<PERSON> from "./CallID";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20_ShopPartItem from "./M20_ShopPartItem";
import $2M20_ShopPartItem_adcoupon from "./M20_ShopPartItem_adcoupon";
import $2M20_ShopPartItem_box from "./M20_ShopPartItem_box";
import $2M20_ShopPartItem_coin from "./M20_ShopPartItem_coin";
import $2M20_ShopPartItem_daily from "./M20_ShopPartItem_daily";
import $2M20_ShopPartItem_hero from "./M20_ShopPartItem_hero";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_PrePare_Shop")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel)
export default class M20_PrePare_Shop extends $2Pop.Pop {
    @property(cc.Node)
    contentNode: cc.Node = null;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    changeListener(t: any) {
        super.changeListener(t);
            $2Notifier.Notifier.changeCall(t, $2CallID.CallID.M20_GetShopView, this.getThis, this);
    }

    onLoad() {
        let e: any;
            let t: any;
            this.shopdata = [{
              title: "每日商店",
              refreshCount: $2Manager.Manager.vo.switchVo.refreshSetting[1],
              refreshCd: $2Manager.Manager.vo.switchVo.refreshSetting[0],
              contentcomp: "ui/ModeBackpackHero/ShopPartItem_tick",
              comp: "M20_ShopPartItem_daily",
              prefabe: "ui/ModeBackpackHero/partitem"
            }];
            (window as any).wonderSdk.hasPay && (e = this.shopdata).push.apply(e, [{
              title: "免广告券",
              prefabe: "ui/ModeBackpackHero/partitem",
              contentcomp: "ui/ModeBackpackHero/ShopPartItem",
              comp: "M20_ShopPartItem_adcoupon"
            }]);
            (t = this.shopdata).push.apply(t, [{
              title: "宝箱",
              prefabe: "ui/ModeBackpackHero/partitemhigh",
              contentcomp: "ui/ModeBackpackHero/ShopPartItem",
              comp: "M20_ShopPartItem_box"
            }, {
              title: "灵石",
              prefabe: "ui/ModeBackpackHero/partitem",
              contentcomp: "ui/ModeBackpackHero/ShopPartItem",
              comp: "M20_ShopPartItem_coin"
            }]);
    }

    loadData() {
        let e: any;
            const t = this;
            (e = {}).M20_ShopPartItem_box = $2M20_ShopPartItem_box.default;
            e.M20_ShopPartItem_adcoupon = $2M20_ShopPartItem_adcoupon.default;
            e.M20_ShopPartItem_coin = $2M20_ShopPartItem_coin.default;
            e.M20_ShopPartItem_daily = $2M20_ShopPartItem_daily.default;
            e.M20_ShopPartItem_hero = $2M20_ShopPartItem_hero.default;
            const o = e;
            const i = function (e) {
              var i = n.shopdata[e];
              $2Manager.Manager.loader.loadPrefab(i.contentcomp, n.node).then(function (n) {
                const r = n;
                const a = r.getComponent($2M20_ShopPartItem.default);
                a || (a = r.addComponent(o[i.comp]));
                r.setAttribute({
                  parent: t.contentNode,
                  zIndex: e
                });
                a.contentnode = r.getChildByName("list");
                a.title = r.getChildByName("title_store").getComponentInChildren(cc.Label);
                a.setData(t.shopdata[e]);
                t.contentNode.getComponent(cc.Layout).updateLayout();
              });
            };
            const n = this;
            for (const r = 0; r < this.shopdata.length; r++) {
              i(r);
            }
    }

    getThis() {
        return this;
    }

    onOpen() {
        this.node.opacity = 0;
            this.loadData();
    }

    onShowFinish() {
        let e: any;
            let t: any;
            null === (t = (e = this.param).showCb) || t === undefined || t.call(e, this.node);
            this.node.opacity = 255;
    }

    setInfo() {
        $2Notifier.Notifier.call($2CallID.CallID.Shop_GetProductList);
    }

}
