import $2CallID from "./CallID";
import $2ListenID from "./ListenID";
import $2Cfg from "./Cfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2WonderSdk from "./WonderSdk";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_Pop_NewEquipUnlock")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup)
export default class M20_Pop_NewEquipUnlock extends $2Pop.Pop {
    @property(cc.Sprite)
    img: cc.Sprite = null;

    @property(cc.Label)
    gname: cc.Label = null;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    changeListener(t: any) {
        super.changeListener(t);
    }

    onClickFrame() {
        this.close();
    }

    onOpen() {
        const e = this;
            const t = $2Cfg.Cfg.RoleUnlock.find({
              id: this.param.id
            });
            $2Manager.Manager.loader.loadSpriteAsync(t.icon).then(function (t) {
              e.img.spriteFrame = t;
            });
            this.gname.string = t.roleName;
    }

    onClose() {
        let e: any;
            let t: any;
            null === (t = (e = this.param).cb) || t === undefined || t.call(e);
            if (15 == $2Manager.Manager.vo.userVo.guideIndex && $2UIManager.UIManager.queue.length <= 1) {
              const o = $2Notifier.Notifier.call($2CallID.CallID.M20_GetMenuView);
              $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
                targetNode: o.toggleContainer.toggleItems[3].node
              });
            }
            const i = $2WonderSdk.WonderSdk._instance.isGoogleAndroid;
            5 == this.mode.rVo.curPassLv && i && $2Manager.Manager.vo.userVo.ispoplikegame && $2UIManager.UIManager.OpenInQueue("ui/ModeCatGame/M3_PopLikeGameView", $2MVC.MVC.openArgs());
    }

}
