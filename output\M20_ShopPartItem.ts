import $2Notifier from "./$2Notifier";
import $2ListenID from "./$2ListenID";
import $2Manager from "./$2Manager";
import $2ModeBackpackHeroModel from "./$2ModeBackpackHeroModel";

const { ccclass, property } = cc._decorator;

@ccclass
export default class M20_ShopPartItem extends cc.Component {
    @property(cc.Node)
    contentnode: cc.Node = null;

    @property(cc.Label)
    title: cc.Label = null;

    data: any = null;
    cloneitem: cc.Node = null;
    content: any[] = [];

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    setData(data: any) {
        this.data = data;
        if (!this.cloneitem) {
            $2Manager.Manager.loader.loadPrefab(data.prefabe).then((prefab: cc.Node) => {
                this.cloneitem = prefab;
                this.refreshData();
            });
        }
    }

    resetView() {
        this.title.string = this.data.title;
    }

    refreshData() {
        this.content = this.getList();
        this.resetView();
    }

    getList(): any[] {
        return [];
    }

    refresh() {}

    onEnable() {
        this.changeListener(true);
    }

    onDisable() {
        this.changeListener(false);
    }

    changeListener(isAdd: boolean) {
        $2Notifier.Notifier.changeListener(isAdd, $2ListenID.ListenID.Item_GoodsChange, this.resetView, this);
    }
}
