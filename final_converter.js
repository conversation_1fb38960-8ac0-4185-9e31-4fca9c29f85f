const fs = require('fs');
const path = require('path');

class FinalJSToTSConverter {
    constructor() {
        this.importMap = new Map();
        this.propertySet = new Set();
    }

    // 解析并转换单个文件
    convertFile(inputPath, outputPath) {
        try {
            const jsContent = fs.readFileSync(inputPath, 'utf8');
            const tsContent = this.convertJSToTS(jsContent);
            
            if (tsContent) {
                fs.writeFileSync(outputPath, tsContent, 'utf8');
                console.log(`✅ ${path.basename(inputPath)} -> ${path.basename(outputPath)}`);
                return true;
            } else {
                console.log(`❌ 转换失败: ${path.basename(inputPath)}`);
                return false;
            }
        } catch (error) {
            console.error(`❌ 错误 ${path.basename(inputPath)}:`, error.message);
            return false;
        }
    }

    // 主转换方法
    convertJSToTS(jsContent) {
        // 重置状态
        this.importMap.clear();
        this.propertySet.clear();

        // 1. 解析导入
        const imports = this.extractImports(jsContent);
        
        // 2. 解析类信息
        const classInfo = this.extractClassInfo(jsContent);
        if (!classInfo) return null;
        
        // 3. 解析装饰器
        const decorators = this.extractDecorators(jsContent);
        
        // 4. 解析属性
        const properties = this.extractProperties(jsContent);
        
        // 5. 解析方法
        const methods = this.extractMethods(jsContent);
        
        // 6. 解析 getter
        const getters = this.extractGetters(jsContent);
        
        // 7. 解析枚举
        const enums = this.extractEnums(jsContent);

        // 8. 解析构造函数
        const constructor = this.extractConstructor(jsContent);

        // 9. 生成 TypeScript 代码
        return this.generateTS(imports, classInfo, decorators, properties, methods, getters, enums, constructor);
    }

    // 提取导入语句
    extractImports(content) {
        const imports = [];
        // 支持多种模块名格式: $2Module, $1$2Module 等
        const regex = /var (\$\d*\$?\d*\w+) = require\("(\w+)"\);/g;
        let match;

        while ((match = regex.exec(content)) !== null) {
            const moduleName = match[1];         // 变量名保持原样 (如 $2Manager, $1$2TConfig)
            const fileName = match[2];           // 文件名不加前缀
            imports.push(`import ${moduleName} from "./${fileName}";`);
            this.importMap.set(moduleName, moduleName);
        }

        return imports;
    }

    // 提取类信息
    extractClassInfo(content) {
        // 尝试匹配 def_ 模式
        let classMatch = content.match(/var def_(\w+) = function \(e\) \{/);

        // 如果没找到，尝试匹配 exp_ 模式
        if (!classMatch) {
            classMatch = content.match(/var exp_(\w+) = function \(e\) \{/);
        }

        if (!classMatch) return null;

        const className = classMatch[1];

        // 查找继承关系 - 支持多种模块名格式
        const extendsMatch = content.match(/\}\((\$\d*\$?\d*\w+)\.(\w+)\);/);
        let baseClass = 'cc.Component';
        if (extendsMatch) {
            baseClass = `${extendsMatch[1]}.${extendsMatch[2]}`;
        }

        return { className, baseClass };
    }

    // 提取装饰器
    extractDecorators(content) {
        const decorators = [];
        
        if (content.includes('ccp_ccclass')) {
            decorators.push('@ccclass');
        }
        
        const menuMatch = content.match(/ccp_menu\("([^"]+)"\)/);
        if (menuMatch) {
            decorators.push(`@menu("${menuMatch[1]}")`);
        }
        
        // MVC 装饰器
        const mvcMatches = content.matchAll(/\$2MVC\.MVC\.(\w+)\(\$2MVC\.MVC\.(\w+)\.(\w+)\)/g);
        for (const match of mvcMatches) {
            decorators.push(`@$2MVC.MVC.${match[1]}($2MVC.MVC.${match[2]}.${match[3]})`);
        }
        
        return decorators;
    }

    // 提取属性
    extractProperties(content) {
        const properties = [];
        const regex = /cc__decorate\(\[ccp_property\(([^)]+)\)\], _ctor\.prototype, "(\w+)", undefined\);/g;
        let match;
        
        while ((match = regex.exec(content)) !== null) {
            const propType = match[1];
            const propName = match[2];
            
            if (!this.propertySet.has(propName)) {
                this.propertySet.add(propName);
                properties.push({
                    name: propName,
                    type: propType,
                    decorator: `@property(${propType})`
                });
            }
        }
        
        return properties;
    }

    // 提取方法
    extractMethods(content) {
        const methods = [];
        
        // 匹配原型方法
        const methodRegex = /_ctor\.prototype\.(\w+) = function \(([^)]*)\) \{/g;
        let match;
        
        while ((match = methodRegex.exec(content)) !== null) {
            const methodName = match[1];
            const params = match[2];
            
            // 查找方法体
            const methodStart = match.index + match[0].length;
            const methodBody = this.extractMethodBody(content, methodStart - 1);
            
            if (methodBody) {
                methods.push({
                    name: methodName,
                    params: this.parseParams(params),
                    body: this.convertMethodBody(methodBody)
                });
            }
        }
        
        return methods;
    }

    // 提取 getter 方法
    extractGetters(content) {
        const getters = [];
        const regex = /Object\.defineProperty\(_ctor\.prototype, "(\w+)", \{\s*get: function \(\) \{\s*return ([^;]+);\s*\}/g;
        let match;

        while ((match = regex.exec(content)) !== null) {
            const getterName = match[1];
            const returnValue = match[2];

            getters.push({
                name: getterName,
                returnValue: this.convertExpression(returnValue)
            });
        }

        return getters;
    }

    // 提取枚举
    extractEnums(content) {
        const enums = [];

        // 查找枚举模式: var enumName; ... (function (e) { ... })(enumName || (enumName = {}));
        const enumVarRegex = /var (\w+);/g;
        const enumFuncRegex = /\(function \(e\) \{([\s\S]*?)\}\)\((\w+) \|\| \(\2 = \{\}\)\);/g;

        // 先找到所有变量声明
        const enumVars = new Set();
        let varMatch;
        while ((varMatch = enumVarRegex.exec(content)) !== null) {
            enumVars.add(varMatch[1]);
        }

        // 然后找到枚举函数
        let enumMatch;
        while ((enumMatch = enumFuncRegex.exec(content)) !== null) {
            const enumBody = enumMatch[1];
            const enumName = enumMatch[2];

            // 检查这个变量是否在前面声明过
            if (enumVars.has(enumName)) {
                const enumValues = [];

                // 解析枚举值: e[e.Name = value] = "Name";
                const valueRegex = /e\[e\.(\w+) = (\d+)\] = "(\w+)";/g;
                let valueMatch;

                while ((valueMatch = valueRegex.exec(enumBody)) !== null) {
                    const name = valueMatch[1];
                    const value = valueMatch[2];
                    enumValues.push({ name, value });
                }

                if (enumValues.length > 0) {
                    enums.push({
                        name: enumName,
                        values: enumValues
                    });
                }
            }
        }

        return enums;
    }

    // 提取构造函数
    extractConstructor(content) {
        // 查找构造函数模式: function _ctor() { ... }
        const ctorRegex = /function _ctor\(\) \{\s*([\s\S]*?)\s*return t;\s*\}/;
        const match = content.match(ctorRegex);

        if (!match) return null;

        const constructorBody = match[1];
        const properties = [];
        const statements = [];

        // 解析属性初始化: t.propertyName = value;
        const propRegex = /t\.(\w+) = ([^;]+);/g;
        let propMatch;

        while ((propMatch = propRegex.exec(constructorBody)) !== null) {
            const propName = propMatch[1];
            const propValue = propMatch[2];

            // 跳过已经在 @property 装饰器中定义的属性 (这些会自动初始化为 null)
            if (!this.propertySet.has(propName)) {
                // 如果不是 null，则需要在构造函数中初始化
                if (propValue !== 'null') {
                    statements.push(`this.${propName} = ${this.convertExpression(propValue)};`);
                }
            }
        }

        return {
            hasConstructor: statements.length > 0,
            statements: statements
        };
    }

    // 提取方法体
    extractMethodBody(content, startIndex) {
        let braceCount = 0;
        let i = startIndex;
        let foundStart = false;
        
        while (i < content.length) {
            if (content[i] === '{') {
                braceCount++;
                foundStart = true;
            } else if (content[i] === '}') {
                braceCount--;
                if (foundStart && braceCount === 0) {
                    break;
                }
            }
            i++;
        }
        
        if (foundStart && braceCount === 0) {
            return content.substring(startIndex + 1, i);
        }
        
        return null;
    }

    // 解析参数
    parseParams(params) {
        if (!params.trim()) return '';
        
        return params.split(',').map(param => {
            const trimmed = param.trim();
            return `${trimmed}: any`;
        }).join(', ');
    }

    // 转换方法体
    convertMethodBody(body) {
        // 基本转换
        body = body.replace(/var (\w+);/g, 'let $1: any;');
        body = body.replace(/var (\w+) = ([^;]+);/g, 'const $1 = $2;');
        
        // 替换 super 调用
        body = body.replace(/e\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$1($2)');
        
        // 替换工具函数
        body = body.replace(/cc__assign/g, 'Object.assign');
        body = body.replace(/cc__spreadArrays\(([^)]+)\)/g, '[...$1]');
        
        // 替换全局对象
        body = body.replace(/\bwonderSdk\b/g, '(window as any).wonderSdk');
        
        // 替换比较操作
        body = body.replace(/undefined === (\w+)/g, '$1 === undefined');
        body = body.replace(/null === (\w+)/g, '$1 === null');
        
        // 替换模块引用
        for (const [moduleName] of this.importMap) {
            const regex = new RegExp(`\\b${moduleName.replace('$', '\\$')}\\b`, 'g');
            body = body.replace(regex, moduleName);
        }
        
        return body.trim();
    }

    // 转换表达式
    convertExpression(expr) {
        return this.convertMethodBody(expr);
    }

    // 生成 TypeScript 代码
    generateTS(imports, classInfo, decorators, properties, methods, getters, enums, constructor) {
        let ts = '';

        // 导入
        ts += imports.join('\n') + '\n\n';

        // 装饰器常量
        ts += 'const { ccclass, property, menu } = cc._decorator;\n\n';

        // 枚举
        if (enums && enums.length > 0) {
            enums.forEach(enumDef => {
                ts += `enum ${enumDef.name} {\n`;
                enumDef.values.forEach((value, index) => {
                    const comma = index < enumDef.values.length - 1 ? ',' : '';
                    ts += `    ${value.name} = ${value.value}${comma}\n`;
                });
                ts += '}\n\n';
            });
        }
        
        // 类装饰器
        if (decorators.length > 0) {
            ts += decorators.join('\n') + '\n';
        }
        
        // 类定义
        ts += `export default class ${classInfo.className} extends ${classInfo.baseClass} {\n`;
        
        // 属性
        properties.forEach(prop => {
            ts += `    ${prop.decorator}\n`;
            ts += `    ${prop.name}: ${prop.type} = null;\n\n`;
        });

        // 构造函数
        if (constructor && constructor.hasConstructor) {
            ts += `    constructor() {\n`;
            ts += `        super();\n`;
            constructor.statements.forEach(statement => {
                ts += `        ${statement}\n`;
            });
            ts += `    }\n\n`;
        }

        // getter
        getters.forEach(getter => {
            ts += `    get ${getter.name}() {\n`;
            ts += `        return ${getter.returnValue};\n`;
            ts += `    }\n\n`;
        });
        
        // 方法
        methods.forEach(method => {
            ts += `    ${method.name}(${method.params}) {\n`;
            
            // 缩进方法体
            const lines = method.body.split('\n');
            lines.forEach(line => {
                if (line.trim()) {
                    ts += `        ${line}\n`;
                } else {
                    ts += '\n';
                }
            });
            
            ts += `    }\n\n`;
        });
        
        ts += '}\n';
        
        return ts;
    }

    // 批量转换
    batchConvert(inputDir, outputDir) {
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const files = fs.readdirSync(inputDir).filter(file => file.endsWith('.js'));
        let successCount = 0;

        console.log(`🚀 开始批量转换 ${files.length} 个文件...\n`);

        files.forEach(file => {
            const inputPath = path.join(inputDir, file);
            const outputPath = path.join(outputDir, file.replace('.js', '.ts'));
            
            if (this.convertFile(inputPath, outputPath)) {
                successCount++;
            }
        });

        console.log(`\n🎉 转换完成! 成功: ${successCount}/${files.length}`);
        return successCount;
    }
}

// 使用示例
if (require.main === module) {
    const converter = new FinalJSToTSConverter();
    
    const args = process.argv.slice(2);
    const inputDir = args[0] || './scripts';
    const outputDir = args[1] || './output_final';
    
    console.log(`📁 输入: ${inputDir}`);
    console.log(`📁 输出: ${outputDir}\n`);
    
    const successCount = converter.batchConvert(inputDir, outputDir);
    
    if (successCount > 0) {
        console.log(`\n✨ 所有文件已转换完成，请检查 ${outputDir} 文件夹`);
        console.log(`💡 建议：转换后请检查代码并根据需要调整类型定义`);
    }
}

module.exports = FinalJSToTSConverter;
