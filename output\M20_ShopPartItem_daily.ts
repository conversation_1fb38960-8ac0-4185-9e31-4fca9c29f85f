import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2Manager from "./Manager";
import $2GameUtil from "./GameUtil";
import $2M20_PartItem from "./M20_PartItem";
import $2M20_ShopPartItem from "./M20_ShopPartItem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20_ShopPartItem_daily extends $2M20_ShopPartItem.default {
    @property(cc.Node)
    btnrefresh: cc.Node = null;

    @property(cc.Label)
    refreshTick: cc.Label = null;

    @property(cc.Label)
    refreshCount: cc.Label = null;

    update(e: any) {
        if (this.data && this.data.refreshCd && this.cloneitem && (this.cacheTick -= e, this.cacheTick < 0)) {
              this.cacheTick = 1;
              $2Manager.Manager.vo.userVo.dailyData.shop_refreshCd -= 1;
              const t = $2GameUtil.GameUtil.formatSeconds($2Manager.Manager.vo.userVo.dailyData.shop_refreshCd);
              this.refreshTick.string = t.str;
              $2Manager.Manager.vo.userVo.shop_refreshTimestamp = new Date().getTime();
              if ($2Manager.Manager.vo.userVo.dailyData.shop_refreshCd <= 0) {
                $2Manager.Manager.vo.userVo.shop_refreshTimestamp = 0;
                $2Manager.Manager.vo.userVo.dailyData.shop_refreshCd = this.data.refreshCd;
                this.cacheTick = 0;
                this.mode.dailyAdpack.del("30000adlimit");
                cc.sys.localStorage.setItem("saveShopList", []);
                this.refreshData(true);
              }
              $2Manager.Manager.vo.saveUserData();
            }
    }

    refreshData(e: any) {
        const t = this;
            e === undefined && (e = true);
            this.mode.cleanShopdata();
            const o = this.mode.shopGoodspck.getList();
            const i = cc.sys.localStorage.getItem("saveShopList", []);
            const n = [];
            i && "" != i && (n = (n = JSON.parse(i)) || []);
            if (n.length > 0) {
              this.content = n;
            } else if (o && o.length > 0) {
              o.forEach(function (e) {
                t.content.push($2Cfg.Cfg.BagShopItem.find({
                  id: e.id
                }));
              });
            } else {
              this.content = this.getList(e);
            }
            if (0 == $2Manager.Manager.vo.userVo.dailyData.shop_refreshCd) {
              $2Manager.Manager.vo.userVo.dailyData.shop_refreshCd = this.data.refreshCd;
              $2Manager.Manager.vo.saveUserData();
            }
            if (0 == $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount) {
              $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount = this.data.refreshCount;
              $2Manager.Manager.vo.saveUserData();
            }
            this.resetView();
    }

    getList(e: any) {
        const t = this;
            e === undefined && (e = true);
            const o = false;
            const i = $2Manager.Manager.vo.userVo.dailyData.shopRefreshList;
            const n = i.indexOf(0);
            if (e && -1 != n) {
              o = true;
              i[n] = 1;
            }
            const r = $2GameUtil.GameUtil.weightGetList($2Cfg.Cfg.BagShopItem.getArray().filter(function (e) {
              return e.weight && e.type == $2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments && $2Cfg.Cfg.RoleUnlock.getArray().filter(function (e) {
                return t.mode.userEquipPack.has(e.id) && 1 == e.type;
              }).map(function (e) {
                return e.id;
              }).includes(e.equipId);
            }), o ? 4 : 5, "weight");
            // if (o) {
            //   const u = $2GameUtil.GameUtil.random(0, 4);
            //   r.splice(u, 0, $2Cfg.Cfg.BagShopItem.find({
            //     type: 22
            //   }));
            // }
            r.splice(0, 0, $2Cfg.Cfg.BagShopItem.find({
              type: $2CurrencyConfigCfg.CurrencyConfigDefine.Diamond
            }));
            if (o) {
              const p = JSON.stringify(r);
              cc.sys.localStorage.setItem("saveShopList", p);
            }
            return r;
    }

    resetView() {
        let t: any;
            let o: any;
            super.resetView();
            this.node.getChildByName("middle").active = true;
            this.btnrefresh.parent.active = this.data.refreshCount && $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount >= 0;
            this.refreshTick.node.parent.parent.active = this.data.refreshCd;
            const i = null !== (t = $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount) && undefined !== t ? t : null === (o = this.data) || o === undefined ? undefined : o.refreshCount;
            this.refreshCount.string = cc.js.formatStr("今日剩余%d次", i);
            for (const n = 0; n < this.content.length; n++) {
              const r = this.content[n];
              const a = 1;
              this.mode.shopGoodspck.has(r.id) || this.mode.shopGoodspck.addGoods(r.id);
              n > 0 && (a = $2GameUtil.GameUtil.getRandomByWeightInArray($2Manager.Manager.vo.switchVo.shopDiscount, 1)[0]);
              const s = this.contentnode.children[n] || cc.instantiate(this.cloneitem);
              s.setAttribute({
                parent: this.contentnode
              });
              s.getComponent($2M20_PartItem.default).setdata(r, false, a);
            }
    }

    refresh() {
        cc.sys.localStorage.setItem("saveShopList", []);
            $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount--;
            0 == $2Manager.Manager.vo.userVo.dailyData.shop_refreshCount && ($2Manager.Manager.vo.userVo.dailyData.shop_refreshCount = -1);
            $2Manager.Manager.vo.saveUserData();
            this.refreshData(false);
    }

}
