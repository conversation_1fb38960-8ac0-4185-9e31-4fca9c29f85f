import CallID from "./CallID";
import MVC from "./MVC";
import Pop from "./Pop";
import Notifier from "./Notifier";
import Manager from "./Manager";
import Game from "./Game";
import ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import M20_ShopPartItem from "./M20_ShopPartItem";
import M20_ShopPartItem_adcoupon from "./M20_ShopPartItem_adcoupon";
import M20_ShopPartItem_box from "./M20_ShopPartItem_box";
import M20_ShopPartItem_coin from "./M20_ShopPartItem_coin";
import M20_ShopPartItem_daily from "./M20_ShopPartItem_daily";
import M20_ShopPartItem_hero from "./M20_ShopPartItem_hero";

const { ccclass, property, menu } = cc._decorator;

interface ShopData {
    title: string;
    refreshCount?: number;
    refreshCd?: number;
    contentcomp: string;
    comp: string;
    prefabe: string;
}

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_PrePare_Shop")
@MVC.uilayer(MVC.eUILayer.Panel)
export default class M20_PrePare_Shop extends Pop {
    @property(cc.Node)
    contentNode: cc.Node = null;

    shopdata: ShopData[] = [];

    get mode() {
        return ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return Game.Game.mgr;
    }

    changeListener(isAdd: boolean) {
        super.changeListener(isAdd);
        Notifier.Notifier.changeCall(isAdd, CallID.CallID.M20_GetShopView, this.getThis, this);
    }

    onLoad() {
        this.shopdata = [{
            title: "每日商店",
            refreshCount: Manager.Manager.vo.switchVo.refreshSetting[1],
            refreshCd: Manager.Manager.vo.switchVo.refreshSetting[0],
            contentcomp: "ui/ModeBackpackHero/ShopPartItem_tick",
            comp: "M20_ShopPartItem_daily",
            prefabe: "ui/ModeBackpackHero/partitem"
        }];

        if ((window as any).wonderSdk.hasPay) {
            this.shopdata.push({
                title: "免广告券",
                prefabe: "ui/ModeBackpackHero/partitem",
                contentcomp: "ui/ModeBackpackHero/ShopPartItem",
                comp: "M20_ShopPartItem_adcoupon"
            });
        }

        this.shopdata.push(
            {
                title: "宝箱",
                prefabe: "ui/ModeBackpackHero/partitemhigh",
                contentcomp: "ui/ModeBackpackHero/ShopPartItem",
                comp: "M20_ShopPartItem_box"
            },
            {
                title: "灵石",
                prefabe: "ui/ModeBackpackHero/partitem",
                contentcomp: "ui/ModeBackpackHero/ShopPartItem",
                comp: "M20_ShopPartItem_coin"
            }
        );
    }

    refreshDailtData() {
        // Empty implementation
    }

    loadData() {
        const componentMap: { [key: string]: any } = {
            M20_ShopPartItem_box: M20_ShopPartItem_box.default,
            M20_ShopPartItem_adcoupon: M20_ShopPartItem_adcoupon.default,
            M20_ShopPartItem_coin: M20_ShopPartItem_coin.default,
            M20_ShopPartItem_daily: M20_ShopPartItem_daily.default,
            M20_ShopPartItem_hero: M20_ShopPartItem_hero.default
        };

        for (let i = 0; i < this.shopdata.length; i++) {
            const shopItem = this.shopdata[i];
            
            Manager.Manager.loader.loadPrefab(shopItem.contentcomp, this.node).then((prefabNode: cc.Node) => {
                const itemNode = prefabNode;
                let component = itemNode.getComponent(M20_ShopPartItem.default);
                
                if (!component) {
                    component = itemNode.addComponent(componentMap[shopItem.comp]);
                }
                
                itemNode.setAttribute({
                    parent: this.contentNode,
                    zIndex: i
                });
                
                component.contentnode = itemNode.getChildByName("list");
                component.title = itemNode.getChildByName("title_store").getComponentInChildren(cc.Label);
                component.setData(this.shopdata[i]);
                
                this.contentNode.getComponent(cc.Layout).updateLayout();
            });
        }
    }

    getThis() {
        return this;
    }

    onOpen() {
        this.node.opacity = 0;
        this.loadData();
    }

    onShowFinish() {
        this.param?.showCb?.call(this.param, this.node);
        this.node.opacity = 255;
    }

    setInfo() {
        Notifier.Notifier.call(CallID.CallID.Shop_GetProductList);
    }
}
