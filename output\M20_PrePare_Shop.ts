import $2Call<PERSON> from "./$2CallID";
import $2MVC from "./$2MVC";
import $2Pop from "./$2Pop";
import $2Notifier from "./$2Notifier";
import $2Manager from "./$2Manager";
import $2Game from "./$2Game";
import $2ModeBackpackHeroModel from "./$2ModeBackpackHeroModel";
import $2M20_ShopPartItem from "./$2M20_ShopPartItem";
import $2M20_ShopPartItem_adcoupon from "./$2M20_ShopPartItem_adcoupon";
import $2M20_ShopPartItem_box from "./$2M20_ShopPartItem_box";
import $2M20_ShopPartItem_coin from "./$2M20_ShopPartItem_coin";
import $2M20_ShopPartItem_daily from "./$2M20_ShopPartItem_daily";
import $2M20_ShopPartItem_hero from "./$2M20_ShopPartItem_hero";

const { ccclass, property, menu } = cc._decorator;

interface ShopData {
    title: string;
    refreshCount?: number;
    refreshCd?: number;
    contentcomp: string;
    comp: string;
    prefabe: string;
}

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_PrePare_Shop")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Panel)
export default class M20_PrePare_Shop extends $2Pop.Pop {
    @property(cc.Node)
    contentNode: cc.Node = null;

    shopdata: ShopData[] = [];

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    changeListener(isAdd: boolean) {
        super.changeListener(isAdd);
        $2Notifier.Notifier.changeCall(isAdd, $2CallID.CallID.M20_GetShopView, this.getThis, this);
    }

    onLoad() {
        this.shopdata = [{
            title: "每日商店",
            refreshCount: $2Manager.Manager.vo.switchVo.refreshSetting[1],
            refreshCd: $2Manager.Manager.vo.switchVo.refreshSetting[0],
            contentcomp: "ui/ModeBackpackHero/ShopPartItem_tick",
            comp: "M20_ShopPartItem_daily",
            prefabe: "ui/ModeBackpackHero/partitem"
        }];

        if ((window as any).wonderSdk.hasPay) {
            this.shopdata.push({
                title: "免广告券",
                prefabe: "ui/ModeBackpackHero/partitem",
                contentcomp: "ui/ModeBackpackHero/ShopPartItem",
                comp: "M20_ShopPartItem_adcoupon"
            });
        }

        this.shopdata.push(
            {
                title: "宝箱",
                prefabe: "ui/ModeBackpackHero/partitemhigh",
                contentcomp: "ui/ModeBackpackHero/ShopPartItem",
                comp: "M20_ShopPartItem_box"
            },
            {
                title: "灵石",
                prefabe: "ui/ModeBackpackHero/partitem",
                contentcomp: "ui/ModeBackpackHero/ShopPartItem",
                comp: "M20_ShopPartItem_coin"
            }
        );
    }

    refreshDailtData() {
        // Empty implementation
    }

    loadData() {
        const componentMap: { [key: string]: any } = {
            M20_ShopPartItem_box: $2M20_ShopPartItem_box.default,
            M20_ShopPartItem_adcoupon: $2M20_ShopPartItem_adcoupon.default,
            M20_ShopPartItem_coin: $2M20_ShopPartItem_coin.default,
            M20_ShopPartItem_daily: $2M20_ShopPartItem_daily.default,
            M20_ShopPartItem_hero: $2M20_ShopPartItem_hero.default
        };

        for (let i = 0; i < this.shopdata.length; i++) {
            const shopItem = this.shopdata[i];

            $2Manager.Manager.loader.loadPrefab(shopItem.contentcomp, this.node).then((prefabNode: cc.Node) => {
                const itemNode = prefabNode;
                let component = itemNode.getComponent($2M20_ShopPartItem.default);

                if (!component) {
                    component = itemNode.addComponent(componentMap[shopItem.comp]);
                }

                itemNode.setAttribute({
                    parent: this.contentNode,
                    zIndex: i
                });

                component.contentnode = itemNode.getChildByName("list");
                component.title = itemNode.getChildByName("title_store").getComponentInChildren(cc.Label);
                component.setData(this.shopdata[i]);

                this.contentNode.getComponent(cc.Layout).updateLayout();
            });
        }
    }

    getThis() {
        return this;
    }

    onOpen() {
        this.node.opacity = 0;
        this.loadData();
    }

    onShowFinish() {
        this.param?.showCb?.call(this.param, this.node);
        this.node.opacity = 255;
    }

    setInfo() {
        $2Notifier.Notifier.call($2CallID.CallID.Shop_GetProductList);
    }
}
