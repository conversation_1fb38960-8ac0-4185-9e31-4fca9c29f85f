import $2CallID from "./CallID";
import $2GameSeting from "./GameSeting";
import $2L<PERSON>en<PERSON> from "./ListenID";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Pop from "./Pop";
import $2Notifier from "./Notifier";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2Game from "./Game";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20Equipitem from "./M20Equipitem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_Pop_EquipInfo")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup)
export default class M20_Pop_EquipInfo extends $2Pop.Pop {
    @property(cc.Prefab)
    equipitem: cc.Prefab = null;

    @property(cc.Node)
    equipNode: cc.Node = null;

    @property(cc.Node)
    equipSpec1: cc.Node = null;

    @property(cc.Node)
    equipSpec1Node: cc.Node = null;

    @property(cc.Node)
    equipSpec2: cc.Node = null;

    @property(cc.Node)
    equipSpec2Node: cc.Node = null;

    @property(cc.Node)
    btnUpgrade: cc.Node = null;

    @property(cc.Label)
    cost: cc.Label = null;

    @property(cc.Label)
    btnUpgradeLabel: cc.Label = null;

    @property(sp.Skeleton)
    levelup: sp.Skeleton = null;

    @property(cc.Label)
    lockTips: cc.Label = null;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    changeListener(t: any) {
        super.changeListener(t);
    }

    onOpen() {
        this.refresh();
    }

    refresh() {
        let e: any;
            const t = this;
            this.equipNode.destroyAllChildren();
            const o = this.param._param.equipid;
            const i = $2Cfg.Cfg.EquipLv.filter({
              equipId: o
            });
            const n = this.mode.getEquipLv(o);
            this.curlvcfg = i.find(function (e) {
              return e.lv == n;
            });
            const r = (null === (e = this.mode.userEquipPack.getItem(o)) || e === undefined ? undefined : e.lv) || 0;
            const a = i[i.length - 1];
            const c = this.curlvcfg.lv >= a.lv;
            this.btnUpgrade.active = null != this.mode.getEquip(this.param._param.equipid);
            this.lockTips.string = this.mode.getEquip(this.param._param.equipid) ? "" : this.getLockDesc(o);
            this.btnUpgradeLabel.string = c ? "已满级" : "升级";
            this.cost.node.parent.active = !c;
            this.cost.string = c ? "" : i.find(function (e) {
              return e.lv == n;
            }).upgradeCost;
            this.btnUpgrade.getComponent(cc.Button).interactable = !c;
            const u = cc.instantiate(this.equipitem);
            u.setParent(this.equipNode);
            u.setPosition(0, 0);
            u.getComponent($2M20Equipitem.default).setInfo(o);
            this.curlvcfg.baseAtr && this.curlvcfg.baseAtr.forEach(function (e, o) {
              const i = $2Cfg.Cfg.Gameatr.get(e[0]);
              const n = e[1];
              const r = e[2];
              const a = t.equipSpec1Node.children[o] || cc.instantiate(t.equipSpec1Node.children[0]);
              a.setAttribute({
                parent: t.equipSpec1Node
              });
              $2Manager.Manager.loader.loadSpriteToSprit(i.icon, a.getComByChild(cc.Sprite, "icon"));
              a.getComByChild(cc.Label, "atrname").string = i.name;
              if (2e3 == i.id) {
                a.getComByChild(cc.RichText, "val").text = cc.js.formatStr("<outline color=black width=3>%s</outline>", $2GameSeting.GameSeting.getSelectVal(n).name);
              } else {
                a.getComByChild(cc.RichText, "val").text = cc.js.formatStr("<outline color=black width=3>" + n + " " + (r ? "<color=#00ff00>+" + r + "</c>" : "") + (i.unit || "") + "</outline>");
              }
            });
            this.equipSpec2Node.children.forEach(function (e) {
              return e.active = false;
            });
            const p = $2Cfg.Cfg.RoleUnlock.get(o);
            p.rarity;
            $2Manager.Manager.vo.switchVo.equip3SelectShow[p.rarity];
            $2Cfg.Cfg.EquipMergeLv.filter({
              equipId: o,
              lv: 4
            }).length;
            i.forEach(function (e, o) {
              if (e.unlockBuff) {
                const i = $2Cfg.Cfg.Buff.get(e.unlockBuff);
                if (i) {
                  const n = t.equipSpec2Node.children[o] || cc.instantiate(t.equipSpec2Node.children[0]);
                  n.setAttribute({
                    parent: t.equipSpec2Node,
                    position: cc.Vec2.ZERO,
                    active: true
                  });
                  n.getComByChild(cc.RichText, "RichText").text = cc.js.formatStr("<outline color=black width=2>Lv.%d</outline>%s", e.lv, i.desc);
                  n.getComByChild(cc.Label, "name").string = i.name;
                  const a = $2GameSeting.GameSeting.getRarity(t.mode.buffmap[i.rarity]).blockImg;
                  $2Manager.Manager.loader.loadSpriteToSprit(a, n.getComByPath(cc.Sprite, "bg_icon_01"));
                  $2Manager.Manager.loader.loadSpriteToSprit(i.icon, n.getComByPath(cc.Sprite, "bg_icon_01/icon"));
                  n.getChildByName("mask").setActive(r < e.lv);
                  n.getComByPath(cc.Label, "mask/lockMsg").string = cc.js.formatStr("%d级解锁", e.lv);
                }
              }
            });
    }

    getLockDesc(e: any) {
        const t = $2Cfg.Cfg.RoleUnlock.find({
              id: e
            });
            if (t) {
              if (1 == t.unlock) {
                return cc.js.formatStr("通关章节%d解锁", t.Count);
              }
              if (2 == t.unlock) {
                return cc.js.formatStr("%d个碎片解锁", t.Count);
              }
              if (3 == t.unlock) {
                return cc.js.formatStr("%d个视频解锁", t.Count);
              }
            }
            return "";
    }

    upgrade() {
        const e = this;
            13 == $2Manager.Manager.vo.userVo.guideIndex && $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
            if (!(this.curlvcfg.lv >= $2Cfg.Cfg.EquipLv.filter({
              equipId: this.curlvcfg.equipId
            }).length)) {
              const t = this.mode.fragmentsPack.getVal(this.curlvcfg.equipId) >= this.curlvcfg.upgradeNeedle;
              const o = this.mode.currencyIsEnough($2CurrencyConfigCfg.CurrencyConfigDefine.Coin, this.curlvcfg.upgradeCost);
              if (t) {
                if (o) {
                  this.mode.fragmentsPack.useUp(this.curlvcfg.equipId, this.curlvcfg.upgradeNeedle);
                  $2Manager.Manager.vo.knapsackVo.useUp($2CurrencyConfigCfg.CurrencyConfigDefine.Coin, this.curlvcfg.upgradeCost);
                  this.levelup.node.setActive(true);
                  this.levelup.clearTracks();
                  this.levelup.setAnimation(1, "animation", false);
                  this.scheduleOnce(function () {
                    e.levelup.node.setActive(false);
                  }, 1);
                  this.mode.userEquipPack.upgrade(this.param._param.equipid);
                  this.refresh();
                  $2Notifier.Notifier.send($2ListenID.ListenID.M20_EquipUpgrade, this.param._param.equipid);
                  $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 9);
                  $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "Upgrade", {
                    UpgradeEquipId: this.curlvcfg.equipId,
                    UpgradeEquipLv: this.curlvcfg.lv
                  });
                } else {
                  $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_Insufficient_Props_Tips", $2MVC.MVC.openArgs().setParam({
                    currencyType: $2CurrencyConfigCfg.CurrencyConfigDefine.Coin
                  }));
                }
              } else {
                $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_Insufficient_Props_Tips", $2MVC.MVC.openArgs().setParam({
                  currencyType: this.curlvcfg.equipId
                }));
              }
            }
    }

    onClose() {
        if (14 == $2Manager.Manager.vo.userVo.guideIndex) {
              const e = $2Notifier.Notifier.call($2CallID.CallID.M20_SelectEquip);
              e.node.getComByPath(cc.ScrollView, "bg/bottom").scrollTo(e.notUnlockBox.parent.children[0].position);
              $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
                targetNode: e.notUnlockBox.parent,
                enableclick: true,
                blockevent: true
              });
            }
    }

    setInfo() {
        if (12 == $2Manager.Manager.vo.userVo.guideIndex) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
              $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
                targetNode: this.btnUpgrade
              });
            }
    }

    onClickFrame() {
        this.close();
    }

}
