import $2CallID from "./$2CallID";
import $2GameSeting from "./$2GameSeting";
import $2ListenID from "./$2ListenID";
import $2Cfg from "./$2Cfg";
import $2CurrencyConfigCfg from "./$2CurrencyConfigCfg";
import $2MVC from "./$2MVC";
import $2Pop from "./$2Pop";
import $2Notifier from "./$2Notifier";
import $2Manager from "./$2Manager";
import $2UIManager from "./$2UIManager";
import $2EaseScaleTransition from "./$2EaseScaleTransition";
import $2Game from "./$2Game";
import $2ModeBackpackHeroModel from "./$2ModeBackpackHeroModel";
import $2M20Equipitem from "./$2M20Equipitem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeBackpackHero/M20_Pop_EquipInfo")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup)
@$2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition)
export default class M20_Pop_EquipInfo extends $2Pop.Pop {
    @property(cc.Prefab)
    equipitem: cc.Prefab = null;

    @property(cc.Node)
    equipNode: cc.Node = null;

    @property(cc.Node)
    equipSpec1: cc.Node = null;

    @property(cc.Node)
    equipSpec1Node: cc.Node = null;

    @property(cc.Node)
    equipSpec2: cc.Node = null;

    @property(cc.Node)
    equipSpec2Node: cc.Node = null;

    @property(cc.Node)
    btnUpgrade: cc.Node = null;

    @property(cc.Label)
    cost: cc.Label = null;

    @property(cc.Label)
    btnUpgradeLabel: cc.Label = null;

    @property(sp.Skeleton)
    levelup: sp.Skeleton = null;

    @property(cc.Label)
    lockTips: cc.Label = null;

    curlvcfg: any = null;

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get game() {
        return $2Game.Game.mgr;
    }

    changeListener(isAdd: boolean) {
        super.changeListener(isAdd);
    }

    onOpen() {
        this.refresh();
    }

    refresh() {
        this.equipNode.destroyAllChildren();
        const equipId = this.param._param.equipid;
        const equipLvConfigs = $2Cfg.Cfg.EquipLv.filter({ equipId: equipId });
        const currentLv = this.mode.getEquipLv(equipId);
        
        this.curlvcfg = equipLvConfigs.find(config => config.lv === currentLv);
        const userEquipLv = this.mode.userEquipPack.getItem(equipId)?.lv || 0;
        const maxLvConfig = equipLvConfigs[equipLvConfigs.length - 1];
        const isMaxLevel = this.curlvcfg.lv >= maxLvConfig.lv;

        this.btnUpgrade.active = this.mode.getEquip(this.param._param.equipid) != null;
        this.lockTips.string = this.mode.getEquip(this.param._param.equipid) ? "" : this.getLockDesc(equipId);
        this.btnUpgradeLabel.string = isMaxLevel ? "已满级" : "升级";
        this.cost.node.parent.active = !isMaxLevel;
        this.cost.string = isMaxLevel ? "" : equipLvConfigs.find(config => config.lv === currentLv).upgradeCost.toString();
        this.btnUpgrade.getComponent(cc.Button).interactable = !isMaxLevel;

        // 创建装备显示
        const equipInstance = cc.instantiate(this.equipitem);
        equipInstance.setParent(this.equipNode);
        equipInstance.setPosition(0, 0);
        equipInstance.getComponent($2M20Equipitem.default).setInfo(equipId);

        // 显示基础属性
        if (this.curlvcfg.baseAtr) {
            this.curlvcfg.baseAtr.forEach((attr: any[], index: number) => {
                const attrConfig = $2Cfg.Cfg.Gameatr.get(attr[0]);
                const baseValue = attr[1];
                const bonusValue = attr[2];
                
                const attrNode = this.equipSpec1Node.children[index] || cc.instantiate(this.equipSpec1Node.children[0]);
                attrNode.setAttribute({ parent: this.equipSpec1Node });
                
                $2Manager.Manager.loader.loadSpriteToSprit(attrConfig.icon, attrNode.getComByChild(cc.Sprite, "icon"));
                attrNode.getComByChild(cc.Label, "atrname").string = attrConfig.name;
                
                if (attrConfig.id === 2000) {
                    attrNode.getComByChild(cc.RichText, "val").text = cc.js.formatStr(
                        "<outline color=black width=3>%s</outline>", 
                        $2GameSeting.GameSeting.getSelectVal(baseValue).name
                    );
                } else {
                    const bonusText = bonusValue ? `<color=#00ff00>+${bonusValue}</c>` : "";
                    const unit = attrConfig.unit || "";
                    attrNode.getComByChild(cc.RichText, "val").text = cc.js.formatStr(
                        `<outline color=black width=3>${baseValue} ${bonusText}${unit}</outline>`
                    );
                }
            });
        }

        // 隐藏所有技能节点
        this.equipSpec2Node.children.forEach(child => child.active = false);

        const roleUnlockConfig = $2Cfg.Cfg.RoleUnlock.get(equipId);
        const rarity = roleUnlockConfig.rarity;
        $2Manager.Manager.vo.switchVo.equip3SelectShow[rarity];
        $2Cfg.Cfg.EquipMergeLv.filter({ equipId: equipId, lv: 4 }).length;

        // 显示技能
        equipLvConfigs.forEach((config: any, index: number) => {
            if (config.unlockBuff) {
                const buffConfig = $2Cfg.Cfg.Buff.get(config.unlockBuff);
                if (buffConfig) {
                    const skillNode = this.equipSpec2Node.children[index] || cc.instantiate(this.equipSpec2Node.children[0]);
                    skillNode.setAttribute({
                        parent: this.equipSpec2Node,
                        position: cc.Vec2.ZERO,
                        active: true
                    });
                    
                    skillNode.getComByChild(cc.RichText, "RichText").text = cc.js.formatStr(
                        "<outline color=black width=2>Lv.%d</outline>%s", 
                        config.lv, 
                        buffConfig.desc
                    );
                    skillNode.getComByChild(cc.Label, "name").string = buffConfig.name;
                    
                    const rarityConfig = $2GameSeting.GameSeting.getRarity(this.mode.buffmap[buffConfig.rarity]);
                    $2Manager.Manager.loader.loadSpriteToSprit(rarityConfig.blockImg, skillNode.getComByPath(cc.Sprite, "bg_icon_01"));
                    $2Manager.Manager.loader.loadSpriteToSprit(buffConfig.icon, skillNode.getComByPath(cc.Sprite, "bg_icon_01/icon"));
                    
                    skillNode.getChildByName("mask").setActive(userEquipLv < config.lv);
                    skillNode.getComByPath(cc.Label, "mask/lockMsg").string = cc.js.formatStr("%d级解锁", config.lv);
                }
            }
        });
    }

    getLockDesc(equipId: number): string {
        const unlockConfig = $2Cfg.Cfg.RoleUnlock.find({ id: equipId });
        if (unlockConfig) {
            if (unlockConfig.unlock === 1) {
                return cc.js.formatStr("通关章节%d解锁", unlockConfig.Count);
            }
            if (unlockConfig.unlock === 2) {
                return cc.js.formatStr("%d个碎片解锁", unlockConfig.Count);
            }
            if (unlockConfig.unlock === 3) {
                return cc.js.formatStr("%d个视频解锁", unlockConfig.Count);
            }
        }
        return "";
    }

    upgrade() {
        if ($2Manager.Manager.vo.userVo.guideIndex === 13) {
            $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
        }

        const maxLv = $2Cfg.Cfg.EquipLv.filter({ equipId: this.curlvcfg.equipId }).length;
        if (this.curlvcfg.lv >= maxLv) {
            return;
        }

        const hasEnoughFragments = this.mode.fragmentsPack.getVal(this.curlvcfg.equipId) >= this.curlvcfg.upgradeNeedle;
        const hasEnoughCoin = this.mode.currencyIsEnough($2CurrencyConfigCfg.CurrencyConfigDefine.Coin, this.curlvcfg.upgradeCost);

        if (hasEnoughFragments) {
            if (hasEnoughCoin) {
                this.mode.fragmentsPack.useUp(this.curlvcfg.equipId, this.curlvcfg.upgradeNeedle);
                $2Manager.Manager.vo.knapsackVo.useUp($2CurrencyConfigCfg.CurrencyConfigDefine.Coin, this.curlvcfg.upgradeCost);
                
                this.levelup.node.setActive(true);
                this.levelup.clearTracks();
                this.levelup.setAnimation(1, "animation", false);
                this.scheduleOnce(() => {
                    this.levelup.node.setActive(false);
                }, 1);
                
                this.mode.userEquipPack.upgrade(this.param._param.equipid);
                this.refresh();
                
                $2Notifier.Notifier.send($2ListenID.ListenID.M20_EquipUpgrade, this.param._param.equipid);
                $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 9);
                $2Notifier.Notifier.send($2ListenID.ListenID.Event_SendEvent, "Upgrade", {
                    UpgradeEquipId: this.curlvcfg.equipId,
                    UpgradeEquipLv: this.curlvcfg.lv
                });
            } else {
                $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_Insufficient_Props_Tips", $2MVC.MVC.openArgs().setParam({
                    currencyType: $2CurrencyConfigCfg.CurrencyConfigDefine.Coin
                }));
            }
        } else {
            $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_Insufficient_Props_Tips", $2MVC.MVC.openArgs().setParam({
                currencyType: this.curlvcfg.equipId
            }));
        }
    }

    onClose() {
        if ($2Manager.Manager.vo.userVo.guideIndex === 14) {
            const selectEquip = $2Notifier.Notifier.call($2CallID.CallID.M20_SelectEquip);
            selectEquip.node.getComByPath(cc.ScrollView, "bg/bottom").scrollTo(selectEquip.notUnlockBox.parent.children[0].position);
            $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
                targetNode: selectEquip.notUnlockBox.parent,
                enableclick: true,
                blockevent: true
            });
        }
    }

    setInfo() {
        if ($2Manager.Manager.vo.userVo.guideIndex === 12) {
            $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Close);
            $2Notifier.Notifier.send($2ListenID.ListenID.Common_Guide_Forcus, {
                targetNode: this.btnUpgrade
            });
        }
    }

    onClickFrame() {
        this.close();
    }
}
