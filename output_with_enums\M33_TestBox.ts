import $2Notifier from "./Notifier";
import $2L<PERSON>enID from "./ListenID";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2Intersection from "./Intersection";
import $2Game from "./Game";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeChains/M33_TestBox")
export default class M33_TestBox extends cc.Component {
    get game() {
        return $2Game.Game.mgr;
    }

    get uiview() {
        return $2UIManager.UIManager.getView("ui/ModeChains/M33_FightUIView");
    }

    onLoad() {
        this.loadList(this.game.pathData.path);
            this.node.getComByPath(cc.EditBox, "bg/bottonUI/EidtBox").string = this.game.pathData.path;
            this.open(true);
    }

    loadList(e: any) {
        const t = this;
            this.select = null;
            this.chainsTestItem.forEach(function (e) {
              return e.unuse();
            });
            this.chainsTestItem.length = 0;
            this.scheduleOnce(function () {
              JSON.parse(e).forEach(function (e, o) {
                t.chainsTestItem.push(new g(t, e, o));
              });
            });
    }

    open(e: any) {
        this.node.setActive(e);
            cc.tween(this.game.gameCamera.camera).to(.3, {
              zoomRatio: e ? .7 : this.game.cameraZoomRatio
            }).start();
            this.uiview.changeToch(!e);
            $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, e);
    }

    onBtn(e: any, t: any) {
        let o: any;
            const i = this;
            switch (t) {
              case "Close":
                this.open(false);
                break;
              case "AddPoint":
                this.select.addPoint();
                this.select.arrangePoint();
                break;
              case "SubPoint":
                if (this.select.box.childrenCount <= 1) {
                  return;
                }
                this.select.box.spliceNode(this.select.box.childrenCount - 1, 1);
                this.select.arrangePoint();
                break;
              case "Export":
                cc.log("输出路径记录:");
                const n = "";
                this.chainsTestItem.forEach(function (e, t) {
                  const o = e.box.children.map(function (e) {
                    return {
                      x: +e.position.x.toFixed(0),
                      y: +(e.position.y - i.game.offsetY).toFixed(0)
                    };
                  });
                  t >= 1 && (n += ",");
                  n += JSON.stringify(o);
                });
                n = "[" + (n = n.replaceAll(',"z":0', "")) + "]";
                console.log(n);
                null === (o = navigator === null || navigator === undefined ? undefined : navigator.clipboard) || o === undefined || o.writeText(n);
                break;
              case "Reset":
                try {
                  const r = this.node.getComByPath(cc.EditBox, "bg/bottonUI/EidtBox").string;
                  this.loadList(r);
                } catch (a) {
                  cc.log("输入错误:");
                }
                break;
              case "AddNewChains":
                this.chainsTestItem.push(new g(this, [], this.chainsTestItem.length));
            }
    }

}
