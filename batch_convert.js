const fs = require('fs');
const path = require('path');

class JSToTSConverter {
    constructor() {
        this.importMap = new Map();
        this.decoratorMap = new Map([
            ['ccp_ccclass', '@ccclass'],
            ['ccp_property', '@property'],
            ['ccp_menu', '@menu']
        ]);
    }

    // 解析 require 语句并转换为 import
    parseImports(content) {
        const imports = [];
        const requireRegex = /var \$2(\w+) = require\("(\w+)"\);/g;
        let match;

        while ((match = requireRegex.exec(content)) !== null) {
            const moduleName = match[1];
            const fileName = match[2];
            imports.push(`import $2${moduleName} from "./$2${fileName}";`);
            this.importMap.set(`$2${moduleName}`, `$2${moduleName}`);
        }

        return imports;
    }

    // 解析类定义
    parseClassDefinition(content) {
        // 查找类定义模式
        const classDefRegex = /var def_(\w+) = function \(e\) \{/;
        const match = content.match(classDefRegex);
        
        if (!match) return null;
        
        const className = match[1];
        
        // 查找继承关系
        const extendsRegex = /\}\(\$2(\w+)\.(\w+)\);/;
        const extendsMatch = content.match(extendsRegex);
        
        let baseClass = 'cc.Component';
        if (extendsMatch) {
            baseClass = `$2${extendsMatch[1]}.${extendsMatch[2]}`;
        }

        return { className, baseClass };
    }

    // 解析装饰器
    parseDecorators(content) {
        const decorators = [];
        
        // 查找 ccclass 装饰器
        if (content.includes('ccp_ccclass')) {
            decorators.push('@ccclass');
        }

        // 查找 menu 装饰器
        const menuRegex = /ccp_menu\("([^"]+)"\)/;
        const menuMatch = content.match(menuRegex);
        if (menuMatch) {
            decorators.push(`@menu("${menuMatch[1]}")`);
        }

        // 查找 MVC 装饰器
        const mvcRegex = /\$2MVC\.MVC\.(\w+)\(\$2MVC\.MVC\.(\w+)\.(\w+)\)/g;
        let mvcMatch;
        while ((mvcMatch = mvcRegex.exec(content)) !== null) {
            decorators.push(`@$2MVC.MVC.${mvcMatch[1]}($2MVC.MVC.${mvcMatch[2]}.${mvcMatch[3]})`);
        }

        return decorators;
    }

    // 解析属性
    parseProperties(content) {
        const properties = [];
        const propRegex = /cc__decorate\(\[ccp_property\(([^)]+)\)\], _ctor\.prototype, "(\w+)", undefined\);/g;
        let match;

        while ((match = propRegex.exec(content)) !== null) {
            const propType = match[1];
            const propName = match[2];
            properties.push({
                name: propName,
                type: propType,
                decorator: `@property(${propType})`
            });
        }

        return properties;
    }

    // 解析方法
    parseMethods(content) {
        const methods = [];
        
        // 查找原型方法
        const methodRegex = /_ctor\.prototype\.(\w+) = function \([^)]*\) \{([\s\S]*?)\n  \};/g;
        let match;

        while ((match = methodRegex.exec(content)) !== null) {
            const methodName = match[1];
            let methodBody = match[2];
            
            // 转换方法体中的引用
            methodBody = this.convertMethodBody(methodBody);
            
            methods.push({
                name: methodName,
                body: methodBody
            });
        }

        return methods;
    }

    // 转换方法体
    convertMethodBody(body) {
        // 替换 e.prototype.xxx.call(this, xxx) 为 super.xxx(xxx)
        body = body.replace(/e\.prototype\.(\w+)\.call\(this,?\s*([^)]*)\)/g, 'super.$1($2)');

        // 替换变量声明
        body = body.replace(/var (\w+);/g, 'let $1: any;');
        body = body.replace(/var (\w+) = ([^;]+);/g, 'const $1 = $2;');

        // 替换 undefined === xxx 为 xxx === undefined
        body = body.replace(/undefined === (\w+)/g, '$1 === undefined');

        // 替换 null === xxx 为 xxx === null
        body = body.replace(/null === (\w+)/g, '$1 === null');

        // 替换 cc__assign 为 Object.assign
        body = body.replace(/cc__assign/g, 'Object.assign');

        // 替换 cc__spreadArrays 为 展开运算符
        body = body.replace(/cc__spreadArrays\(([^)]+)\)/g, '[...$1]');

        // 替换 wonderSdk 为 (window as any).wonderSdk
        body = body.replace(/\bwonderSdk\b/g, '(window as any).wonderSdk');

        // 替换模块引用
        for (const [oldRef, newRef] of this.importMap) {
            const regex = new RegExp(`\\b${oldRef.replace('$', '\\$')}\\b`, 'g');
            body = body.replace(regex, newRef);
        }

        // 添加类型注解
        body = body.replace(/function \(([^)]*)\) \{/g, '($1) => {');

        // 替换 forEach 回调参数类型
        body = body.replace(/\.forEach\(function \(([^,)]+)(?:,\s*([^)]+))?\)/g, '.forEach(($1: any$2) =>');

        return body;
    }

    // 生成 TypeScript 代码
    generateTypeScript(jsContent, fileName) {
        const imports = this.parseImports(jsContent);
        const classInfo = this.parseClassDefinition(jsContent);
        const decorators = this.parseDecorators(jsContent);
        const properties = this.parseProperties(jsContent);
        const methods = this.parseMethods(jsContent);

        if (!classInfo) {
            console.warn(`无法解析类定义: ${fileName}`);
            return null;
        }

        let tsContent = '';
        
        // 添加 imports
        tsContent += imports.join('\n') + '\n\n';
        
        // 添加装饰器常量
        tsContent += 'const { ccclass, property, menu } = cc._decorator;\n\n';
        
        // 添加类装饰器
        tsContent += decorators.join('\n') + '\n';
        
        // 添加类定义
        tsContent += `export default class ${classInfo.className} extends ${classInfo.baseClass} {\n`;
        
        // 添加属性
        properties.forEach(prop => {
            tsContent += `    ${prop.decorator}\n`;
            tsContent += `    ${prop.name}: ${prop.type} = null;\n\n`;
        });
        
        // 添加方法
        methods.forEach(method => {
            if (method.name === 'constructor') return; // 跳过构造函数
            
            tsContent += `    ${method.name}() {\n`;
            tsContent += method.body.split('\n').map(line => '    ' + line).join('\n');
            tsContent += '\n    }\n\n';
        });
        
        tsContent += '}\n';
        
        return tsContent;
    }

    // 转换单个文件
    convertFile(inputPath, outputPath) {
        try {
            const jsContent = fs.readFileSync(inputPath, 'utf8');
            const tsContent = this.generateTypeScript(jsContent, path.basename(inputPath));
            
            if (tsContent) {
                fs.writeFileSync(outputPath, tsContent, 'utf8');
                console.log(`✅ 转换成功: ${path.basename(inputPath)} -> ${path.basename(outputPath)}`);
                return true;
            } else {
                console.log(`❌ 转换失败: ${path.basename(inputPath)}`);
                return false;
            }
        } catch (error) {
            console.error(`❌ 转换出错 ${path.basename(inputPath)}:`, error.message);
            return false;
        }
    }

    // 批量转换
    batchConvert(inputDir, outputDir) {
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const files = fs.readdirSync(inputDir).filter(file => file.endsWith('.js'));
        let successCount = 0;
        let totalCount = files.length;

        console.log(`开始批量转换 ${totalCount} 个文件...\n`);

        files.forEach(file => {
            const inputPath = path.join(inputDir, file);
            const outputPath = path.join(outputDir, file.replace('.js', '.ts'));
            
            if (this.convertFile(inputPath, outputPath)) {
                successCount++;
            }
        });

        console.log(`\n转换完成! 成功: ${successCount}/${totalCount}`);
    }
}

// 使用示例
const converter = new JSToTSConverter();

// 批量转换
converter.batchConvert('./scripts', './output');

// 或者转换单个文件
// converter.convertFile('./scripts/M20_PartItem.js', './output/M20_PartItem.ts');
