import $2ListenID from "./$2ListenID";
import $2Cfg from "./$2Cfg";
import $2MVC from "./$2MVC";
import $2Notifier from "./$2Notifier";
import $2UIManager from "./$2UIManager";
import $2M20_PartItem from "./$2M20_PartItem";
import $2M20_ShopPartItem from "./$2M20_ShopPartItem";

const { ccclass } = cc._decorator;

@ccclass
export default class M20_ShopPartItem_box extends $2M20_ShopPartItem.default {
    bar: cc.Node = null;

    onLoad() {
        this.bar = this.node.getChildByName("middle").getChildByName("boxbar");
        this.bar.active = true;
        this.bar.getChildByName("btninfo").on(cc.Node.EventType.TOUCH_START, this.showBoxInfo, this);
        $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.M20_UpdateBoxExp, this.checkUpgrade, this);
        
        if (!this.mode.fightinfopack.has("boxlv")) {
            this.mode.fightinfopack.addGoods("boxlv");
        }
    }

    showBoxInfo() {
        $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_ShopBoxInfo", $2MVC.MVC.openArgs().setIsNeedLoading(false));
    }

    get boxLv(): number {
        return this.mode.fightinfopack.getVal("boxlv") || 1;
    }

    onDestroy() {
        this.bar.getChildByName("btninfo").off(cc.Node.EventType.TOUCH_START, this.showBoxInfo, this);
    }

    checkUpgrade() {
        const currentExp = this.mode.fightinfopack.getVal("boxcost");
        const requiredExp = $2Cfg.Cfg.BoxLevelExp.get(this.boxLv).BoxlvExp;
        
        if (this.boxLv <= $2Cfg.Cfg.BoxLevelExp.getArray().length && currentExp >= requiredExp) {
            this.mode.fightinfopack.setVal("boxcost", currentExp - requiredExp);
            this.mode.fightinfopack.addGoods("boxlv");
            
            const openArgs = $2MVC.MVC.openArgs();
            openArgs.setIsNeedLoading(false);
            openArgs.setParam({
                content: {
                    from: this.boxLv
                }
            });
            
            if (this.boxLv <= $2Cfg.Cfg.BoxLevelExp.getArray().length) {
                $2UIManager.UIManager.OpenInQueue("ui/ModeBackpackHero/M20_Pop_ShopBoxInfo", openArgs);
            }
        }
        
        this.refreshData();
    }

    getList(): any[] {
        return [...$2Cfg.Cfg.BagShopItem.getArray().filter(item => {
            return item.id === 10000 + this.boxLv || item.id === 10100 + this.boxLv;
        })];
    }

    resetView() {
        super.resetView();
        this.node.getChildByName("middle").active = true;
        
        if (!this.bar) {
            this.bar = this.node.getChildByName("middle").getChildByName("boxbar");
        }
        
        // this.contentnode.getComponent(cc.Layout).paddingLeft = 38;
        
        let isMaxLevel = false;
        let requiredExp = 0;
        
        if (this.boxLv > $2Cfg.Cfg.BoxLevelExp.getArray().length) {
            isMaxLevel = true;
        } else {
            requiredExp = $2Cfg.Cfg.BoxLevelExp.get(this.boxLv).BoxlvExp;
        }
        
        const currentExp = this.mode.fightinfopack.getVal("boxcost");
        
        cc.tween(this.bar.getChildByName("progress").getComponent(cc.ProgressBar))
            .to(0.5, { progress: isMaxLevel ? 1 : currentExp / requiredExp })
            .call(() => {
                this.bar.getChildByName("lv").getComponent(cc.Label).string = "LV." + 
                    (isMaxLevel ? $2Cfg.Cfg.BoxLevelExp.getArray().length : this.mode.fightinfopack.getVal("boxlv"));
            })
            .start();
        
        this.bar.getChildByName("progress").getComponentInChildren(cc.Label).string = 
            isMaxLevel ? "已满级" : currentExp + "/" + requiredExp;
        
        for (let i = 0; i < this.content.length; i++) {
            const itemData = this.content[i];
            const itemNode = this.contentnode.children[i] || cc.instantiate(this.cloneitem);
            itemNode.setAttribute({ parent: this.contentnode });
            itemNode.getComponent($2M20_PartItem.default).setdata(itemData, true, 1);
        }
    }
}
