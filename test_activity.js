const FinalJSToTSConverter = require('./final_converter');
const fs = require('fs');

// 测试 activityCfg.js 的转换
const converter = new FinalJSToTSConverter();

// 读取文件内容
const jsContent = fs.readFileSync('./scripts/activityCfg.js', 'utf8');

// 转换
const tsContent = converter.convertJSToTS(jsContent);

if (tsContent) {
    // 写入文件
    fs.writeFileSync('./activityCfg_fixed.ts', tsContent, 'utf8');
    console.log('✅ 转换成功！');
    
    // 显示结果
    console.log('\n转换结果:');
    console.log(tsContent);
} else {
    console.log('❌ 转换失败');
}
