import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2UIManager from "./UIManager";
import $2FightScene from "./FightScene";
import $2Game from "./Game";
import $2MChains from "./MChains";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeChains/M33_FightScene")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Scene)
@$2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Scene)
export default class M33_FightScene extends $2FightScene.FightScene {
    get role() {
        return this.game.mainRole;
    }

    changeListener(t: any) {
        super.changeListener(t);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_End, this.onOpenEndView, this);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_Win, this.onFightWin, this);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_OpenReliveView, this.onOpenReliveView, this);
    }

    setInfo() {
        const e = this;
            if (this.game) {
              this.game.destroy();
              this.game = null;
            }
            this.game = new $2MChains.MChains.Mgr(this._openArgs.param || {});
            this.game.loadMap(this.gameNode, function () {
              $2UIManager.UIManager.Open("ui/ModeChains/M33_FightUIView", $2MVC.MVC.openArgs().setIsNeedLoading(false).setOpenCallback(function () {}));
              e.game.gameState = $2Game.Game.State.START;
            });
    }

    onFightWin() {
        this.onOpenEndView(true);
    }

    onOpenEndView(e: any) {
        $2UIManager.UIManager.Open("ui/ModeChains/M33_Pop_GameEnd", $2MVC.MVC.openArgs().setParam({
              isWin: e,
              cfg: this.game.miniGameCfg
            }).setDailyTime(.2).setIsNeedLoading(false));
    }

    onOpenReliveView() {
        if (this.game.gameState != $2Game.Game.State.PAUSE) {
              $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
              $2UIManager.UIManager.Open("ui/ModeChains/M33_Pop_Revive", $2MVC.MVC.openArgs());
            }
    }

}
