import $2CallID from "./CallID";
import $2Cfg from "./Cfg";
import $2CurrencyConfigCfg from "./CurrencyConfigCfg";
import $2MVC from "./MVC";
import $2Notifier from "./Notifier";
import $2Pop from "./Pop";
import $2GameSeting from "./GameSeting";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2UIManager from "./UIManager";
import $2EaseScaleTransition from "./EaseScaleTransition";
import $2GameUtil from "./GameUtil";
import $2AlertManager from "./AlertManager";
import $2Game from "./Game";
import $2ItemModel from "./ItemModel";
import $2MoreGamesView from "./MoreGamesView";
import $2ModeChainsModel from "./ModeChainsModel";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("ViewComponent/ModeChains/M33_Pop_GameEnd")
@$2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup)
export default class M33_Pop_GameEnd extends $2Pop.Pop {
    @property([cc.Sprite])
    imgArr: [cc.Sprite] = null;

    constructor() {
        super();
        this.rewardList = [];
    }

    get game() {
        return $2Game.Game.mgr;
    }

    get mode() {
        return $2ModeChainsModel.default.instance;
    }

    setInfo() {
        let e: any;
            let t: any;
            const o = this;
            const i = this.param.isWin;
            const n = this.param.cfg || this.game.miniGameCfg;
            const r = n.type >= 10;
            const a = i ? 1 : this.game.killMonsterNum / this.game.totalLen;
            const c = Math.floor(100 * a);
            this.mode.levelVo.setLvMaxRound(n.lvid, c, i);

            // $2Manager.Manager.loader.loadSpriteToSprit("img/common/" + (i ? "img_slbt" : "img_sbbt"), this.imgArr[0]);
            // $2Manager.Manager.loader.loadSpriteToSprit("img/ModeChains/ui/" + (i ? "img_sl" : "img_sb"), this.imgArr[1]);

            if (i) {
              this.imgArr[0].node.setActive(true);
              this.imgArr[1].node.setActive(false);
            } else {
              this.imgArr[0].node.setActive(false);
              this.imgArr[1].node.setActive(true);
            }

            const l = this.nextCfg = $2Cfg.Cfg.BagModeLv.get(this.mode.levelVo.CurChallengeLv);
            this.nodeArr[4].hideAllChildren();
            this.nodeArr[4].getChildByName("BackToMain").setActive(false);
            if (i) {
              if (l.lvid == n.lvid) {
                this.nodeArr[4].getChildByName("btn_playagain").setActive(true);
              } else {
                this.nodeArr[4].getChildByName("btn_next").setActive(true);
              }
            } else {
              this.nodeArr[4].getChildByName("btn_playagain").setActive(true);
            }
            r && this.nodeArr[4].setActive(false);
            const u = 1;
            if (i && r) {
              const p = this.game.curActivity;
              u = $2Manager.Manager.leveMgr.vo.curPassLv - p.unlockChapter;
              if (p.gameWinReward) {
                const f = $2GameUtil.GameUtil.deepCopy(p.gameWinReward);
                const h = $2ItemModel.default.instance.briefRewardArrTo(f);
                (e = this.rewardList).push.apply(e, h);
              }
            }
            const y = this.mode.getRewardlist(n.lvid);
            (t = this.rewardList).push.apply(t, y);
            this.rewardList.forEach(function (e) {
              e.num = Math.ceil(e.num * a * (1 + e.param * u));
            });
            this.nodeArr[3].getComponent(cc.ProgressBar).progress = a;
            this.nodeArr[3].getComByChild(cc.Label, "num").string = Math.floor(100 * a) + "%";
            this.isVideoReward = false;
            this.nodeArr[0].hideAllChildren();
            this.rewardList.forEach(function (e, t) {
              if (!(e.num <= 0)) {
                const i = o.nodeArr[0].children[t] || cc.instantiate(o.nodeArr[0].children[0]);
                i.setAttribute({
                  parent: o.nodeArr[0],
                  active: true
                });
                o.setItemInfo(e, i);
              }
            });
            this.labelArr[0].string = "-" + $2Manager.Manager.vo.switchVo.fightStamina;
            this.labelArr[1].string = "-" + $2Manager.Manager.vo.switchVo.fightStamina;
            if (!this.game) {
              this.nodeArr[4].setActive(false);
              this.nodeArr[3].setActive(false);
            }
            $2UIManager.UIManager.Close("ui/ModeChains/M33_FightBuffView");
            $2UIManager.UIManager.cleanQueue();
            $2Manager.Manager.ui.cleanDelayView();
    }

    setItemInfo(e: any, t: any) {
        const o = e.type == $2GameSeting.GameSeting.GoodsType.Money ? $2Cfg.Cfg.CurrencyConfig.get(e.id) : $2Cfg.Cfg.RoleUnlock.get(e.id);
            $2Manager.Manager.loader.loadSpriteToSprit(o.icon, t.getComByChild(cc.Sprite, "icon"));
            $2Manager.Manager.loader.loadSpriteToSprit($2GameSeting.GameSeting.getRarity(e.rarity).blockImg, t.getComByChild(cc.Sprite, "bg"));
            t.getComByChild(cc.Label, "val").string = e.num * (this.isVideoReward ? 2 : 1);
            cc.find("icon/fragments", t).setActive(e.type == $2GameSeting.GameSeting.GoodsType.Fragment);
            cc.tween(t).stopLast().set({
              opacity: 0
            }).delay(.1 * t.zIndex).to(.3, {
              opacity: 255
            }, {
              easing: cc.easing.backOut
            }).start();
    }

    onClickDamagePanel(e: any) {
        const t = this;
            const o = e.target;
            if (1 == o.childrenCount) {
              o.children[0].setActive(!o.children[0].active);
            } else {
              if (99 == o.t_tempID) {
                return;
              }
              o.t_tempID = 99;
              $2Manager.Manager.loader.loadPrefab("ui/fight/DamagePanel", this.game.gameNode).then(function (e) {
                t.DamagePanel = e;
                e.setAttribute({
                  parent: o,
                  position: cc.v2(-e.children[0].width / 2 * 1.5, 500),
                  scale: 1.5
                });
              });
            }
    }

    onBtn(e: any, t: any) {
        const o = this;
            switch (t) {
              case "Replay":
                $2Notifier.Notifier.call($2CallID.CallID.Item_User, {
                  type: $2CurrencyConfigCfg.CurrencyConfigDefine.Energy,
                  val: $2Manager.Manager.vo.switchVo.fightStamina,
                  call: function (e) {
                    if (e == $2GameSeting.GameSeting.ProgressCode.COMPLETE) {
                      $2Notifier.Notifier.send($2ListenID.ListenID.Game_Replay);
                      o.close();
                    }
                  }
                });
                break;
              case "NextLevel":
                this.nextCfg && $2Notifier.Notifier.call($2CallID.CallID.Item_User, {
                  type: $2CurrencyConfigCfg.CurrencyConfigDefine.Energy,
                  val: $2Manager.Manager.vo.switchVo.fightStamina,
                  call: function (e) {
                    if (e == $2GameSeting.GameSeting.ProgressCode.COMPLETE) {
                      $2Notifier.Notifier.send($2ListenID.ListenID.Game_NextLV, $2MVC.MVC.openArgs().setParam({
                        id: o.nextCfg.lvid
                      }));
                      o.close();
                    }
                  }
                });
                break;
              case "BackToMain":
                $2Notifier.Notifier.send($2ListenID.ListenID.Fight_BackToMain);
                this.close();
            }
    }

    onVideoUnlock(e: any, t: any) {
        const o = +t;
            const i = $2Cfg.Cfg.MiniGameLv.get(o);
            const n = i.type;
            $2Manager.Manager.vo.knapsackVo.addGoods("ModeUnlockVideo_" + n + "_" + i.lvid);
            if ($2Manager.Manager.vo.knapsackVo.has("ModeUnlockVideo_" + n + "_" + i.lvid) >= $2MoreGamesView.MoreGames.getUnlockNum(i.lvid)) {
              $2Manager.Manager.vo.userVo.unLockMode.push(n + "_" + i.lvid);
              $2Manager.Manager.vo.saveUserData();
              $2AlertManager.AlertManager.showNormalTips("解锁成功");
            }
            this.setInfo();
    }

    onClickFrame() {
        let e: any;
            (null === (e = this.DamagePanel) || e === undefined ? undefined : e.active) && this.DamagePanel.setActive(false);
    }

    onClose() {
        $2Notifier.Notifier.send($2ListenID.ListenID.Item_GetReward, this.rewardList, false);
            super.onClose();
    }

}
