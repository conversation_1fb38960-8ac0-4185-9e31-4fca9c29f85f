import $2Call<PERSON> from "./CallID";
import $2Cfg from "./Cfg";
import $2Notifier from "./Notifier";
import $2ListenID from "./ListenID";
import $2Manager from "./Manager";
import $2OrganismBase from "./OrganismBase";
import $2Game from "./Game";
import $2MBackpackHero from "./MBackpackHero";
import $2ModeBackpackHeroModel from "./ModeBackpackHeroModel";
import $2M20Prop_Equip from "./M20Prop_Equip";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20Prop extends $2OrganismBase.default {
    @property(cc.Sprite)
    img: cc.Sprite = null;

    @property(sp.Skeleton)
    mySkeleton: sp.Skeleton = null;

    get game() {
        return $2Game.Game.mgr;
    }

    get mode() {
        return $2ModeBackpackHeroModel.default.instance;
    }

    get role() {
        return this.game.mainRole;
    }

    get roleID() {
        return this.roleCfg.id;
    }

    get figureData() {
        return {
        Atk: 0,
        Hp: 0,
        Def: 0
      };
    }

    get figureNextData() {
        return {
        Atk: 0,
        Hp: 0,
        Def: 0
      };
    }

    get prorSkillId() {
        return null;
    }

    get nextLv() {
        return null;
    }

    get preparationBlockList() {
        return this._preparationBlockList;
    }

    get saveData() {
        return {
        id: this.mergeCfg.id,
        pos: this.position,
        curHp: this.curHp,
        isUnlock: this.isUnlock
      };
    }

    removePromotelEffect() {
        if (this.promotelEffect) {
              this.promotelEffect.destroy();
              this.promotelEffect = null;
            }
    }

    changeListener(t: any) {
        super.changeListener(t);
            this.changeToch(t);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.ModeBack_PackState, this.onModeBack_PackState, this, $2Notifier.PriorLowest);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_OnSkillChange, this.onFight_OnSkillChange, this, $2Notifier.PriorLowest);
            $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_RoundState, this.onFight_RoundState, this);
    }

    changeToch(e: any) {
        this.node.changeListener(e, cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
            this.node.changeListener(e, cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
            this.node.changeListener(e, cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
            this.node.changeListener(e, cc.Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    onModeBack_PackState(e: any) {
        let t: any;
            (null === (t = this.blockList) || t === undefined ? undefined : t.length) && this.resetPos();
            this.isCanClick = 1 == e || 2 == e && this.propType == $2MBackpackHero.MBPack.PropType.Block;
            this.node.opacity = this.isCanClick ? 255 : 100;
            this.changeToch(this.isCanClick);
    }

    onTouchStart() {
        if (this.isCanClick) {
              this.packView.operationID = this.ID;
              this.node.setParent(this.packView.MoveBox);
              cc.tween(this.node).to(.1, {
                angle: 0
              }).start();
              this.packView.removeInPack(this);
              this.isTouch = true;
              this.blockList.length = 0;
              $2Notifier.Notifier.call($2CallID.CallID.ModeBack_CheckPoit, this.seachPoint, this);
              this.packView.sortSpareBox();
              this._frameNum = 0;
              this.packView.selectProp = this;
            }
    }

    onTouchMove(e: any) {
        if (this.isCanClick) {
              this._frameNum++;
              const t = e.getPreviousLocation();
              const o = e.getLocation();
              o.subSelf(t);
              this.node.setPosition(this.position.add(o));
              $2Notifier.Notifier.call($2CallID.CallID.ModeBack_CheckPoit, this.seachPoint, this);
            }
    }

    onTouchEnd(e: any) {
        if (this.isCanClick && this.packView.operationID == this.ID) {
              $2Notifier.Notifier.call($2CallID.CallID.ModeBack_CheckPoit, this.seachPoint, this, true);
              this.isTouch = false;
              this.packView.onTouchEnd(e, this);
              this.packView.selectProp = null;
            }
    }

    resetPos() {
        const e = [];
            this.blockList.forEach(function (t) {
              e.push(t.curPos);
            });
            const t = this.getGridPos(this.blockType, e).clone();
            cc.tween(this.node).to(.1, {
              position: t
            }).start();
    }

    setVideoLock() {
        if (0 != this.isUnlock) {
              this.isUnlock = false;
              const e = [31, 32].includes(this.blockType) ? "img/ModeBackpackHero/gezi_bubble_01" : [33, 34, 35, 36].includes(this.blockType) ? "img/ModeBackpackHero/gezi_bubble_02" : [21, 22].includes(this.blockType) ? "img/ModeBackpackHero/gezi_bubble_03" : undefined;
              this.img.node.removeAllChildren();
              this.propType == $2MBackpackHero.MBPack.PropType.Block && $2Manager.Manager.loader.loadSpriteImg(e, {
                nodeAttr: {
                  parent: this.img.node,
                  angle: {
                    22: 90,
                    21: 0,
                    31: 0,
                    32: 90,
                    33: 0,
                    34: -90,
                    35: -180,
                    36: -270
                  }[this.blockType],
                  position: cc.v2(),
                  zIndex: 1,
                  opacity: this.isUnlock ? 0 : 255
                }
              }, this.node.parent);
              $2Manager.Manager.loader.loadSpriteImg("img/ui/icon_ads", {
                nodeAttr: {
                  parent: this.node,
                  angle: 0,
                  opacity: this.isUnlock ? 0 : 255,
                  name: "VideoUnlockImg",
                  position: cc.v2(.2 * -this.blockSize.width, .3 * this.blockSize.height),
                  zIndex: 2,
                  scale: .8
                }
              }, this.node.parent);
            }
    }

    setUnlock() {
        let e: any;
            this.isUnlock = true;
            this.img.node.removeAllChildren();
            null === (e = this.node.getChildByName("VideoUnlockImg")) || e === undefined || e.destroy();
            this.game.saveRecordVo();
            this.propType == $2MBackpackHero.MBPack.PropType.MergeEquipment && this.game.recordVo.vo.Lv3EquipAppear--;
    }

    init() {
        super.init();
            this.img = this.node.getComByChild(cc.Sprite, "icon");
            this.mySkeleton = this.node.getComByChild(sp.Skeleton, "ske");
    }

    set(e: any, t: any) {
        let o: any;
            this.blockList.length = 0;
            this.isCanClick = true;
            this.hasReadData = t;
            this.roleCfg = $2Cfg.Cfg.RoleUnlock.get(e.equipId);
            this.propType = [null, $2MBackpackHero.MBPack.PropType.MergeEquipment, $2MBackpackHero.MBPack.PropType.Block, null, $2MBackpackHero.MBPack.PropType.Gem][this.roleCfg.type];
            1e4 == this.roleCfg.id && (this.propType = $2MBackpackHero.MBPack.PropType.MergeEquipment);
            this.mergeCfg = e;
            this.resetBlockType((null == t ? undefined : t.blockType) || this.roleCfg.grid);
            this.img.node.setPosition(cc.v2(0, 0));
            this.node.getChildByName("name").setActive(false);
            this.node.getChildByName("num").setActive(false);
            const i = !e.adUnlock;
            t && (i = t.isUnlock);
            if (i) {
              null === (o = this.node.getChildByName("VideoUnlockImg")) || o === undefined || o.destroy();
              this.isUnlock = i;
            } else {
              this.setVideoLock();
            }
    }

    resetBlockType(e: any) {
        const t = this;
            this.blockType = e;
            this.pointList.length = 0;
            $2MBackpackHero.MBPack.BlockPos[this.blockType].forEach(function (e) {
              const o = e.mul($2MBackpackHero.MBPack.BlockSize);
              t.pointList.push(o);
            });
            const o = this.getGridPos(this.blockType, this.pointList).clone();
            this.pointList.forEach(function (e) {
              e.subSelf(o);
              t.node.setContentSize(Math.abs(2 * e.x) + $2MBackpackHero.MBPack.BlockSize, Math.abs(2 * e.y) + $2MBackpackHero.MBPack.BlockSize);
              t.blockSize = t.node.getContentSize();
            });
    }

    resetState(e: any) {
        let t: any;
            let o: any;
            const i = this;
            this.node.getChildByName("lv").setAttribute({
              position: cc.v2(this.blockSize.width / 2 - 20, this.blockSize.height / 2 - 20),
              active: this.propType == $2MBackpackHero.MBPack.PropType.MergeEquipment
            });
            $2Manager.Manager.loader.loadSpriteToSprit("img/ModeBackpackHero/mergeLv" + this.mergeCfg.lv, this.node.getComByChild(cc.Sprite, "lv"));
            this.mergeCfg.spine && $2Manager.Manager.loader.loadSpine(this.mergeCfg.spine, this.packView.node).then(function (e) {
              let t: any;
              if (i.mySkeleton.isValid) {
                i.mySkeleton.reset(e);
                i.mySkeleton.setPause(false);
                i.setAnimation("animation", true);
                null === (t = i.buffMgr) || t === undefined || t.use(8e3, false, function (e) {
                  const t = e.tempData.SwallowIDs && [null, "01", "02", "03"][e.tempData.SwallowIDs.length];
                  if (t) {
                    i.setAnimation(t, true);
                  } else {
                    i.mySkeleton.animation = null;
                  }
                });
              }
            });
            null === (t = this.mySkeleton) || t === undefined || t.node.setAttribute({
              position: this.img.node.position,
              active: !!this.mergeCfg.spine
            });
            null === (o = this.img) || o === undefined || o.node.setActive(!this.mergeCfg.spine);
            if (this.roleCfg.grid != this.blockType && e) {
              const n = $2MBackpackHero.MBPack.BlockSize / Math.max(e.width, e.height);
              this.img.node.scale = n;
              this.mySkeleton.node.scale = n;
            }
    }

    showTips(e: any, t: any) {
        const o = this;
            e === undefined && (e = "<outline color=black width=2>点击查看宝石详情</outline>");
            t === undefined && (t = false);
            if (!(!t && this.game.rVo.isGemTips.includes(this.roleCfg.id))) {
              $2Manager.Manager.loader.loadPrefab("ui/ModeBackpackHero/TipsItem").then(function (t) {
                t.getComByPath(cc.RichText, "New Sprite/New Label").text = e;
                cc.tween(t).set({
                  parent: o.node,
                  position: cc.v2(0, 0),
                  scale: 0
                }).by(.1, {
                  y: o.img.node.height / 2,
                  scale: 1
                }).delay(2).to(.1, {
                  scale: 0
                }).call(function () {
                  t.destroy();
                }).start();
              });
              this.game.rVo.isGemTips.push(this.roleCfg.id);
            }
    }

    unuse() {
        super.unuse();
            this.isDead = true;
            this.packView.removeInPack(this);
            this.packView.retsetAllList();
            this.destroy();
            this.node.destroy();
    }

    getGridPos(e: any, t: any) {
        v.set(cc.Vec2.ZERO);
            if ([33, 34, 35, 36].includes(e)) {
              v.addSelf(t[1]).addSelf(t[2]).divSelf(2);
            } else {
              t.forEach(function (e) {
                return v.addSelf(e);
              });
              v.divSelf(t.length);
            }
            50 == e && v.addSelf(cc.v2(0, .3 * -$2MBackpackHero.MBPack.BlockSize));
            return v;
    }

    getAroundEquip(e: any) {
        const t = this;
            const o = new Set();
            ((null == e ? undefined : e.map(function (e) {
              return e.position;
            })) || this.curPoint).forEach(function (e) {
              [cc.v2(1, 0), cc.v2(-1, 0), cc.v2(0, 1), cc.v2(0, -1)].forEach(function (i) {
                const n = i.mulSelf($2MBackpackHero.MBPack.BlockSize).addSelf(e);
                t.packView.propList.forEach(function (e) {
                  e.ID != t.ID && e instanceof $2M20Prop_Equip.default && e.blockList.forEach(function (t) {
                    cc.Vec2.squaredDistance(n, t.position) < Math.pow($2MBackpackHero.MBPack.BlockSize, 1.9) && o.add(e);
                  });
                });
              });
            });
            return o;
    }

    checkReaction(e: any, t: any) {
        t === undefined && (t = false);
            return null;
    }

    checkBuffEffect() {
        return null;
    }

    OnBuff(e: any) {
        let t: any;
            this.updateProperty();
            null === (t = this.skillMgr) || t === undefined || t.onBuff(e);
    }

}
