import $2ListenID from "./ListenID";
import $2Notifier from "./Notifier";
import $2M20Equipitem from "./M20Equipitem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20EquipitemList extends $2M20Equipitem.default {
    @property(cc.Node)
    dropNode: cc.Node = null;

    @property(cc.Node)
    btndel: cc.Node = null;

    @property(cc.Node)
    btnuse: cc.Node = null;

    onEnable() {
        this.changeListener(true);
    }

    onDisable() {
        this.changeListener(true);
    }

    changeListener(e: any) {
        $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Refresh_Item, this.resetState, this, -200);
            $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Item_GoodsChange, this.resetState, this, -200);
    }

    setInfo(t: any) {
        t === undefined && (t = this.equipcfg.id);
            super.setInfo(t);
            this.oldZindex = this.node.zIndex;
            this.resetState();
    }

    resetState(t: any) {
        super.resetState();
            this.btndel.setActive(this.isEquip);
            this.btnuse.setActive(!this.isEquip);
            this.dropNode.setActive(false);
            this.node.zIndex = this.oldZindex;
            cc.tween(this.node).stopLast().to(.1, {
              scale: 1,
              opacity: 255
            }).start();
            if (2 == this.view.stateType) {
              if (this.isEquip) {
                cc.tween(this.node).stopLast().to(.3, {
                  scale: 1.1
                }).to(.3, {
                  scale: 1
                }).union().repeatForever().start();
              } else if (this == t) {
                cc.tween(this.node).stopLast().to(.1, {
                  scale: 1.1
                }).start();
              } else {
                cc.tween(this.node).to(.1, {
                  opacity: 100
                }).start();
              }
            }
    }

    onClick() {
        if (2 != this.view.stateType || this.isEquip) {
              this.dropNode.setActive(!this.dropNode.active);
              this.node.zIndex = this.dropNode.active ? 999 : this.oldZindex;
              this._onClickCall && this._onClickCall(this);
            }
    }

    select() {
        this.view.onSelectItem(this);
    }

    delete() {
        this.mode.UnAssembleEquip(this.equipID);
            $2Notifier.Notifier.send($2ListenID.ListenID.Refresh_List);
    }

}
