import $2Cfg from "./Cfg";
import $2M20Equipitem from "./M20Equipitem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
export default class M20EquipitemBlock extends $2M20Equipitem.default {
    @property(cc.Label)
    lockinfo: cc.Label = null;

    setInfo(t: any) {
        super.setInfo(t);
            const o = $2Cfg.Cfg.EquipLv.filter({
              equipId: t
            });
            const i = $2Cfg.Cfg.RoleUnlock.find({
              id: t
            });
            this.equipcfg = i;
            this.eueiplvcfgs = o;
    }

    resetState() {
        super.resetState();
            const t = this.mode.fragmentsPack.getVal(this.equipcfg.id);
            if (1 == this.equipcfg.unlock) {
              this.lockinfo.string = cc.js.formatStr("通关章节%d解锁", this.equipcfg.Count);
            } else if (2 == this.equipcfg.unlock) {
              this.lockinfo.string = cc.js.formatStr("%d个碎片解锁", this.equipcfg.Count - t);
            } else {
              3 == this.equipcfg.unlock && (this.lockinfo.string = cc.js.formatStr("%d个视频解锁", this.equipcfg.Count));
            }
    }

}
